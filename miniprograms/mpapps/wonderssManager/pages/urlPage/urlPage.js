// pages/urlPage/urlPage.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    webViewUrl:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('options', options)
    let globalData = getApp().globalData
    let url = globalData.webViewRoot
    Object.keys(options).forEach((k) => {
      console.log(k, options[k])
      url += '&' + k + '=' + options[k]
    });
    url += '&navigationBarTitleText=' + globalData.navigationBarTitleText
    url = url.replace('&', '?')
    console.log('this.webViewUrl=', url )
    this.setData({ webViewUrl: url })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})