// pages/pay/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    payData: {},
    order: {},
  },
 
  onLoad: function (options) { 
    let that = this
    console.log('options', options)
    let data = JSON.parse(decodeURIComponent(options.data))
    console.log('data', data)
    this.setData('payData', data?.payData)
    this.setData('order', data?.order)
    if(data?.payData) {
      let payData = data.payData
      let orderId = data?.order?.id
      let url = data?.url
      payData.success =
      payData.fail = function (res) {
        console.log('pay result', encodeURIComponent(JSON.stringify(res)))
        wx.navigateTo({
          url: '/pages/urlPage/index?url='+url+'&orderId='+orderId+'&payRes='+encodeURIComponent(JSON.stringify(res))
        });
      }
      wx.requestPayment(payData)
    } else {
      console.error('代码错误，没有获取到预下单数据！')
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
   
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    var link = encodeURIComponent(this.data.src)
    return {
      path: '/pages/pay/index?link=' + link
    }
  }
})