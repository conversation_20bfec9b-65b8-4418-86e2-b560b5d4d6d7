// pages/start/start.js
import localStorage from '../../localStorage'
Page({

    /**
     * 页面的初始数据
     */
    data: {
        canIUseGetUserProfile: wx.canIUse('getUserProfile'),
        isControl: '',
        userInfo: '',
        code: '',
        appId: '',
        tgCode: ''
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getIsControl()
        if(options.reloadcode) {
            setTimeout(()=>{this.wxLogin()},0)
        }
        // wx.setNavigationBarTitle({  title: '当前页面'})

    },
    getUserProfile(e) { 
        let that = this
        wx.getUserProfile({
            desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
            success: (res) => {
                console.log('展示用户信息',res)
                let userInfo = encodeURIComponent(JSON.stringify(res.userInfo))
                that.setData({userInfo: userInfo})
                localStorage.setItem('userInfo', userInfo)
            },
            complete: () => {
                that.login()
            }
        })
    },
    generateMixed(n) {
        var str = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'p', 'q', 't', 'u', 'v', '', 'x', 'y', 'z',
            // '@','#','$','¥','%','^','&','*','<','>','?','{','+','=','_','-'
        ];
        var res = "";
        let length = str.length - 1
        for (var i = 0; i < n + 1; i++) {
            var id = Math.ceil(Math.random() * length);
            res += str[id];
        }
        let insertStr = 'wonderss';
        let insertStrLen = insertStr.length;
        for (let j = 0; j < insertStrLen; j++) {
            let len = parseInt(res.length / insertStrLen)
            let startIndex = j * len;
            // let endIndex = (j+1) * len;
            let selectIndex = Math.round(Math.random() * len + startIndex);
            let insertA = insertStr[j];
            res = `${res.substring(0, selectIndex)}${insertA}${res.substring(selectIndex)}`;
        }
        return res;
    },
    getIsControl() {
        let that = this
        let url = 'https://dvm05.btransmission.com/tp/tpAppGetToken.do';
        var app = getApp();
        var globalData = app.globalData
        that.setData({
            appId: globalData.appId,
            tgCode: globalData.tgCode
        })
        let secretKey = that.generateMixed(92)
        let data = {
            'appId': globalData.appId,
            'secretKey': secretKey,
            'appVersion': globalData.mpVersion,
            'envType': globalData.envType
        }
        wx.request({
            url: url,
            data: data,
            type: 'POST',
            header: {
                'content-type': 'application/json' // 默认值
            },
            success(res) {
                that.isControl = res.data
            }
        })
    },
    goNext() {
        let isControl = this.isControl
        let len = isControl?.length || 0
        console.log('len=', len)
        // len = 16
        if (len === 16) { // yeah  shenheqi
            wx.switchTab({
                url: '../homePage/my/my'
            })
        } else { // error
            this.error()
        }
    },
    login() {
        let that = this
        wx.checkSession({
            success(res) {
                let code = localStorage.getItem('sessionCode')
                if (code != null) {
                    that.setData({code: code})
                    that.goNext()
                } else {
                    console.log('  wx.checkSession success  else',)
                    that.wxLogin()
                }
            },
            fail() {
                console.log('  wx.checkSession fail',)
                that.wxLogin()
            }
        })
    },
    wxLogin() {
        let that = this
        wx.login({
            success: res => {
                // console.log('登陆 res=', res);
                that.setData({code: res.code})
                localStorage.setItem('sessionCode', res.code)
            },
            complete: res => {
                that.goNext()
            }
        })
    },
    error() {
        let path = '../urlPage/urlPage'
        let params = Object.entries(this.data).filter(([key]) => ['appId','tgCode','code','userInfo'].includes(key))
        if(params.length>0) {
            path += '?' + params.map(([key, value]) => `${key}=${value}`).join('&')
        }
        console.log('path=', path)
        wx.navigateTo({
            url: path
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})