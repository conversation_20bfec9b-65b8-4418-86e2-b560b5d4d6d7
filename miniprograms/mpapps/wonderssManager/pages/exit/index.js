// pages/exit/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {},
  onLoad(options) {
    setTimeout(()=>{this.exit()}, 5000)
  },
  exit () {
    wx.exitMiniProgram({
      success: function () {
        console.log('退出小程序成功')
        wx.closeWindow()
      },
      fail: function (err) {
        console.error('退出小程序失败', err)
        wx.navigateBack({
          delta: 1000 // 返回的页面数，确保大于当前页面栈的深度
        })
      },
      complete: function () {
        console.log('退出小程序操作结束')
      }
    })
  }
})