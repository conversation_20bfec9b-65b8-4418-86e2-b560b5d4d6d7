// pages/homePage/my/my.js
import localStorage from '../../../localStorage'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo:{ 'nickName': '‘昵称',  }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    try {
      let userInfo = JSON.parse(decodeURIComponent(localStorage.getItem('userInfo') || '{}'))
      console.log('userInfo=', userInfo)
      this.setData({ 'userInfo': userInfo })
    } catch (e) {
      // Do something when catch error
    }

   
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },
  goPage(){
    console.log('go')
    wx.showToast({
      title: '敬请期待',
      icon: 'error',
      duration: 2000
    });
  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})