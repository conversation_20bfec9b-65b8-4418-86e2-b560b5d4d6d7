/* pages/homePage/my/my.wxss */
.my-flex-panel{
  display: flex; padding:20px; background-color: #fafafa; margin-bottom: 20px;
}
.img33{
  flex: 1; display: inline-block; overflow: hidden; background-color: #fafafa;
}
.img33 .image{ background-color: #eee; border-radius: 80px; width:80px; height:80px; position: relative; top: 20px; }
.user{
  flex: 3;
  padding-left: 15px;
}
.officeName{ font-size: 1.1em; line-height: 50px; color: #53b5a8; font-weight:bold;  }
.userInfo{ line-height:30px; color: #555; font-size: 0.9em; }

.houseSty8{ position: relative; top:5px; }
.houseSty9{ position: relative; top:3px; }
.icon-privateArea{ background-image: url('/assets/my/privateArea.png'); }
.icon-appFee{ background-image: url("/assets/my/appFee.png"); }
.icon-about{ background-image: url("/assets/my/about.png"); }
.icon-changeOffice{ background-image: url("/assets/my/changeOffice.png"); }
.icon-loginOffTeam{ background-image: url("/assets/my/loginOffTeam.png"); background-size: 15px }
.icon-cancellation{ background-image: url("/assets/my/cancellation.png"); }
.icon{
  width: 20px;
  height:20px;
  background-color: #fff;
  background-size: 100%;
  display: inline-block;
  position: relative;
  top:6px;
  margin-right:14px;
  background-repeat: no-repeat;
}
.mar30 {
  margin-top: 30px;
}





