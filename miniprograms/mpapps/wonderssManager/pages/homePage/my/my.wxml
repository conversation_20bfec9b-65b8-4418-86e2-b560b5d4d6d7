<!--pages/homePage/my/my.wxml-->
<!-- 数据 -->
<view class="mainContainer">
    <view class="homeMy">
		<view class="my-flex-panel my-flex-item">
			<view class="img33">
				<image class="image" src="{{userInfo.avatarUrl}}" mode="头像"/>
			</view>
			<view class="user">
				<view class="officeName">{{ userInfo.nickName }}的领地</view>
				<view class="userInfo">
					<view class="txt">账号：{{ userInfo.nickName || '未知' }}</view>
					<view>
						<text class="txt">
                            {{ userInfo.country ||'地域未知' }} {{userInfo.province }} {{userInfo.city }}
                        </text>
					</view>
				</view>
			</view>
		</view>
        <view>
            <view class="live-panel live-tr">
                <view data-address='mine' bind:tap="goPage">
                    <image class="icon" src="/assets/my/privateArea.png"></image>
                    通讯录
                </view>
            </view>
            <!-- <view class="live-panel live-tr">
                <view data-address='useFee' bind:tap="goPage">
                    <image class="icon" src="/assets/my/appFee.png"></image>
                    软件使用费
                </view>
            </view> -->
            <view class="live-panel live-tr">
                <view  data-address='about'  bind:tap="goPage">
                <image class="icon" src="/assets/my/about.png"></image>
                关于
                </view>
            </view>
            <view class="live-panel live-pad" style="display: none;">
                <view class="live-tr live-tr-line">
                    <text class="icon icon-changeOffice"></text>切换工作室
                    <text class="live-right">
                        {{ officeList.length }}个工作室 
                    </text>
                </view>
                <view class="live-tr" >
                    <text class="icon icon-cancellation"></text>注销工作室
                </view>
                <view  class="live-tr" >
                    <text class="icon icon-loginOffTeam"></text>     
                </view>
            </view>
        </view>
    </view>
</view>