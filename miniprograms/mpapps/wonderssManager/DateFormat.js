
/**
 * 将js Date对象格式化为指定格式,添加一个原型方法
 * 返回指定format的string
 * 使用如:
 * new Date().format('yyyy-MM-dd');    // echo: '2015-12-01'
 * new Date().format('yyyy年MM月dd日');    // echo: '2015年12月01'
 * new Date().format('yyyy/MM/dd');    // echo: '2015/12/01'
 * new Date("Tue Oct 25 2018 09:05:51 GMT+0800").format('yyyy年MM月dd日')   ;
 * new Date("2018-05-05 8:00:56.0").format('yyyy年M月d日')  ;
 * new Date( 1540170634600 ).format('MM月dd日')  ;
 * new Date( 1540170634600 ).format('yyyy-MM-dd hh:mm:ss')  ;
 * WuYu 2019/3/19 13:55 修改说明 如果Date值为null或者0，返回空字符串。
 **/


 Date.prototype.format = function(format) {
    if(this.getTime()>0) {
        var o = {
            "M+": this.getMonth() + 1,
            "d+": this.getDate(),
            "h+": this.getHours(),
            "m+": this.getMinutes(),
            "s+": this.getSeconds(),
            "q+": Math.floor((this.getMonth() + 3) / 3),
            "S": this.getMilliseconds()
        };
        if (/(y+)/.test(format)) {
            format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(format)) {
                format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
            }
        }
    } else {
        format = '';
    }
    return format;
};