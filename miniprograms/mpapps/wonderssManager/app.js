// app.js
import localStorage from 'localStorage'

import 'DateFormat.js'
const miniProgram = wx?.getAccountInfoSync ? wx.getAccountInfoSync().miniProgram : tt?.getEnvInfoSync().microapp
// console.log('miniProgram=', miniProgram)

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = localStorage.getItem('logs') || []
    logs.unshift(Date.now())
    localStorage.setItem('logs', logs)
  },
  globalData: {
    userInfo: null,
    // webViewRoot: 'https://dvm05.btransmission.com/vue/wonderssManager/dist/index.html',
    webViewRoot: 'https://dvm01.btransmission.com/vue/wonderssManager/dist/index.html',
    // webViewRoot: 'https://wonderss.hongbeibz.com/vue/wonderssManager/dist/index.html',
    tgCode: 'weChat', // wonderss 管家
    appId: miniProgram.appId,
    navigationBarTitleText: 'wonderss管家',
    mpVersion: 'SVN_REVISION',
    envType: miniProgram.envVersion ?? miniProgram.envType
  }
})