import 'DateFormat.js'
const miniProgram = wx?.getAccountInfoSync ? wx.getAccountInfoSync().miniProgram : tt?.getEnvInfoSync().microapp


App({
  onLaunch: function () {
  },
  globalData: {
    // webViewRoot: 'http://localhost:443',
    // webViewRoot: 'https://hxz.frp.btransmission.com',
    // webViewRoot: 'https://hxz-t.frp.btransmission.com/vue/liveMiniApp/dist/index.html',
    // webViewRoot: 'https://wuyu-n.frp.btransmission.com',
    // webViewRoot: 'https://dvm05.btransmission.com/vue/liveMiniApp/dist/index.html',
    // webViewRoot: 'https://wonderss.hongbeibz.com/vue/liveMiniApp/dist/index.html',
    webViewRoot: 'https://dvm01.btransmission.com/vue/liveMiniApp/dist/index.html',

    tgCode: "byteDance",
    appId: miniProgram.appId,
    mpVersion: 'SVN_REVISION',
    envType: miniProgram.envType
  }
})
