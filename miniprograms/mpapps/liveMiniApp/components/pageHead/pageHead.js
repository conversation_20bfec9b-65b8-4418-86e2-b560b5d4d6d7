
Component({
  properties: {
    // define headerText property to use in custom component
    headerText: {
      type: String,
      value: "",
    },
  },
  data: {
    title:'',
    // component internal data
    defaultStates: {},
  },
  attached: function () {
    let that = this
    // 在组件实例进入页面节点树时执行
    tt.getStorage({
      key: "userInfo",
      success(res) {
        console.log('缓存取出 userInfo =', res)
        that.setData({ title :  `${res.data.nickName}的工作室`  })
      },
      fail(res) {
        console.log('缓存取出 userInfo失败 = ', res);
      },
    });
  },
  methods: {
    // Component internal method
    customMethod: function () {},
  },
});