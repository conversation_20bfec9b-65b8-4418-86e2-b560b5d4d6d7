
Component({
  properties: {
    // define headerText property to use in custom component
    headerText: {
      type: String,
      value: "default title",
    },
  },
  data: {
    // component internal data
    defaultStates: {},
  },
  methods: {
    // Component internal method
    customMethod: function () {},
    goPage: function (env) {
      console.log('env=', env)
      let address = env.currentTarget.dataset.address
      console.log('address=', address)
      switch (address){
        case 'life':
          tt.navigateTo({ 'url': '/pages/mine/privateSpace/life/life' })
          break;
        case 'mailList':
          tt.navigateTo({ 'url': '/pages/mine/privateSpace/mailList/mailList' })
          break;
        case 'manage':
          tt.navigateTo({
            url: '/pages/mine/privateSpace/manage/manage',
          }); 
          break;
        case 'remind':
          tt.navigateTo({
            url: '/pages/mine/privateSpace/remind/remind',
          }); 
          break;
        case 'my':
          tt.navigateTo({
            url: '/pages/mine/privateSpace/my/my',
          }); 
          break;
  
      }
  
    },
  },
});