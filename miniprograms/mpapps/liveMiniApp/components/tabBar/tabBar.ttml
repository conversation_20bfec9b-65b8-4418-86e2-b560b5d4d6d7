<view class="tabBarComponent">
    <view class="fixContainer">
 
        <view bindtap="goPage" data-address="life">
            <image class="icon icon-handle" src="/assets/my/tabbar/life.png"></image>  
            <text>生活</text>
        </view>
        <view bindtap="goPage" data-address="mailList">
            <image class="icon icon-handle" src="/assets/my/tabbar/mailList.png"></image>  
            <text>通讯录</text>
        </view>
        
        <view bindtap="goPage" data-address="manage">
            <image class="icon icon-handle" src="/assets/my/tabbar/manage1.png"></image>  
            <text>管理</text>
        </view>
        
        <view bindtap="goPage" data-address="remind">
            <image class="icon icon-handle" src="/assets/my/tabbar/remind.png"></image>
            <text>提醒</text>
        </view>

        <view bindtap="goPage" data-address="my">
            <image class="icon icon-handle" src="/assets/my/tabbar/my1.png"></image>
            <text>我的</text>
        </view>


    </view>

</view>