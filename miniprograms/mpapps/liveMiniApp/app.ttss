*{ font-size: 16px; }
#app{
    background-color: #eee;
}
.live-container{  padding:10px 16px; }
.live-button{display: block;line-height: 40px;text-align: center;border-radius: 4px;margin-top: 10px;}
.primary{
    background:  #53b5a8; 
    color:#fff;
    border:1px solid rgba(83,181,168, 0.2);
}
.cancel{color:#666;border:1px solid #999;}

.live-panel{ background:#fff; color:#333; padding:10px 16px; margin-bottom: 16px; }
.live-title{ line-height: 40px; }
.live-tr{ line-height: 30px; }
.live-title{ line-height: 40px; }
.live-tr.live-tr-line{ border-bottom: 1px solid #eaeaea;  }
.live-panel.live-pad .live-tr{
  padding:6px 0;
}



.pageHead {
    height: 44px;
    background-color: #53b5a8;
    line-height: 44px;
    color: #fff;
    font-size: 16px;
    position: relative;
    text-align: center;
    
  }
  .btns {
    width: 50px;
    position: absolute;
    right: 0;
    top:0;
  }
  .back, .close{
    width: 60px;
    position: absolute;
  }
  .back {
    left: 0;
  }
  .close {
    left:40px;
  }
  .title {
    text-align: center;
  }





.live-container{  padding:10px 16px; }
.live-panel{ background:#fff; color:#333; padding:10px 16px; margin-bottom: 16px; }
.live-tr{ line-height: 30px; }
.live-title{ line-height: 40px; }
.live-tr.live-tr-line{ border-bottom: 1px solid #eaeaea;  }
.live-panel.live-pad .live-tr{
  padding:6px 0;
}
.live-panel.live-pad .live-tr:not(:first-child), .live-panel.live-pad .live-tr:not(:last-child){
  padding:9px 0;
}
.live-right{ float: right; }
.live-bottomDiv{ position: fixed; bottom: -21px; width: 100%; background: #fff; left: 0; padding: 5px 20px 10px; box-sizing: border-box; }
.line-link{ color: #3f89db;  }

/* // 敬请期待 */
.wating{ display: block; height: 200px; width: 200px; margin: 200px auto 40px; background-repeat: no-repeat!important; opacity: 0.4; background-position:center center;   }
.watTxt{ color: #ccc; font-size: 36px; text-align: center;  }

.txtInfo{
  line-height: 30px;
  color: #444;
}
.tab2{
  margin-left: 16px;
}

