Page({
  data: {
    shareMsg:{}
  },
    onLoad: function () {
        let that = this
        tt.getStorage({
            key: "share",
            success(res) {
                console.log('缓存取出 share =', res)
                that.setData({  shareMsg: res.data  })
                // this.shareMsg = res.data
            },
            fail(res) {
                console.log('缓存取出 share 失败 = ', res);
            },
        })
        // shareName: "王肉肉是奶萌哒"
        // shareTime: "1688350475220"
        // type: "1"
    },
    back(){
        const type = this.data.shareMsg.type
        const oid = this.data.shareMsg.oid
        tt.redirectTo({
            url:`/pages/index/index?shareback=${ type }&oid=${ oid }`
        })
    },
    onShareAppMessage(option) {
        console.log('onShareAppMessage(option)=', option)
        const title = '字幕精灵';
        let smg = this.data.shareMsg
        const type = smg.type
        const oid = smg.oid
        const content = 'adfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfdsadfsfsdfsfds'
        const path = `/pages/start/start?shareName=${ smg.shareName }&shareTime=${ smg.shareTime }&type=${ type }&ss=1&content=${ content }`
        console.log('share url=', path)
        return {
            title,
            path,
            // desc: "这是默认的转发文案，用户可以直接发送，也可以在发布器内修改",
            // imageUrl: "https://e.com/e.png", // 支持本地或远程图片，默认是小程序 icon
            // templateId: "这是开发者后台设置的分享素材模板id",
            success() {
                console.log("转发发布器已调起，并不意味着用户转发成功，微头条不提供这个时机的回调");
                tt.redirectTo({
                    url:`/pages/index/index?shareback=${ type }&oid=${ oid }`
                })
            },
            fail() {
                console.log("转发发布器调起失败");
            },
        };
    },
})