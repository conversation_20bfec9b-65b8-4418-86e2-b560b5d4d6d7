Page({
  data: {
    shareMsg:{}
  },
  onLoad: function (options) {
    let that = this
    tt.getStorage({
        key: "share",
        success(res) {
            console.log('缓存取出 share =', res)
            that.setData({  shareMsg: res.data  })
            // this.shareMsg = res.data
        },
        fail(res) {
            console.log('缓存取出 share 失败 = ', res);
        },
    })
  },
  back(){
    let smg = this.data.shareMsg
    const type = smg.type
    const oid = smg.oid
    tt.redirectTo({
        url:`/pages/index/index?shareback=${ type }&oid=${ oid }`
    })
},
  onShareAppMessage(option) {
    console.log('onShareAppMessage(option)=', option)
    let smg = this.data.shareMsg
    const title = '字幕精灵';
    const type = smg.type
    const oid = smg.oid
    const path = `/pages/start/start?shareName=${ smg.shareName }&shareTime=${ smg.shareTime }&type=${ type }&oid=${ oid }&ss=1`
    console.log('share url=', path)
    return {
      title,
      path,
      success() {
        console.log("转发发布器已调起，并不意味着用户转发成功，微头条不提供这个时机的回调");
        tt.redirectTo({
          url:`/pages/index/index?shareback=${ type }&oid=${ oid }`
        })
      },
      fail() {
        console.log("转发发布器调起失败");
      },
    };
  },
})