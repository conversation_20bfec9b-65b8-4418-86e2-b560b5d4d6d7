// /Users/<USER>/Documents/ideaProject/VSCproject/java/miners_trunk/miniprograms/mpapps/live/pages/start/start.js
Page({
  data: {
    hasSession: false,
    userInfo: null,
    shareOption: {},
    isControl: '',
  },
  onLoad: function (options) {
    let that = this

    tt.getStorage({
      key: "userInfo",
      success(res) {
        console.log('缓存取出 userInfo =', res)
        // jsonData.userInfo = res.data
        that.setData({ userInfo :  res.data  })
      },
      fail(res) {
        console.log('缓存取出 userInfo失败 = ', res);
        that.setData({ userInfo :  null  })
      },
    });


    console.log('转发过来的参数', options)
    if(options.type === '1' || options.type === '2'){ // 大分享 小分享
      that.setData({  shareOption: options  })
      this.shareOption = options
      console.log('shareOption===', this.shareOption)
      tt.setStorage({
        key: "share",
        data: options,
        success(res) {
          console.log(`save 转发过来的参数`);
        },
        fail(res) {
          console.log(`save 转发过来的参数 fail`);
        },
      });
    }

    tt.checkSession({
      success: res => {
        console.log('checkSession success =', res)
       this.setData({  hasSession: true })
       if(that.shareOption){
         if(options.ss === '1'){ // 点击别人的分享
          console.log('点击别人的分享')
          tt.redirectTo({
            url:`/pages/index/index`
          })
         }else{ // 自己要分享出去的
          console.log('自己要分享出去的')
          if(that.shareOption.type === '1'){ // 大分享
            tt.redirectTo({
              url:`/pages/bigShare/bigShare`
            })
          }else if(that.shareOption.type === '2'){
            tt.redirectTo({
              url:`/pages/litShare/litShare`
            })
          }
         }
       
      }else{
        console.log('不是分享过来的')
        // if(this.data.userInfo){
        //   console.log('能获取到用户信息')
        // }else{
          console.log('不能获取到用户信息，重新登录的')
          tt.login({
            force: true,
            success(res) {
              console.log('login success', res)
              tt.setStorage({
                key: "code",
                data: res.code,
                success(res) {
                  console.log(`save code`);
                  if(that.shareOption){
                    if(options.ss === '1'){ // 点击别人的分享
                      console.log('点击别人的分享')
            
                     }else{ // 自己要分享出去的
                      if(that.shareOption.type === '1'){ // 大分享
                        tt.redirectTo({
                          url:`/pages/bigShare/bigShare`
                        })
                      }else if(that.shareOption.type === '2'){
                        tt.redirectTo({
                          url:`/pages/litShare/litShare`
                        })
                      }
                    }
                   
                  }else{
                   console.log('不是分享过来的')
                  }
                },
                fail(res) {
                  console.log(`save code 失败`);
                },
              });
            },
            fail(res) {
              console.log('Login fail', res)
            }
          })
        // }
      
       
      }
      },
      fail: err =>{
        console.log('checkSession fail =', err)
        // this.setData({  hasSession: false  })
         tt.login({
          force: true,
          success(res) {
            console.log('login success', res)
            tt.setStorage({
              key: "code",
              data: res.code,
              success(res) {
                console.log(`save code`);
                if(that.shareOption){
                  if(options.ss === '1'){ // 点击别人的分享
                    console.log('点击别人的分享')
          
                   }else{ // 自己要分享出去的
                    if(that.shareOption.type === '1'){ // 大分享
                      tt.redirectTo({
                        url:`/pages/bigShare/bigShare`
                      })
                    }else if(that.shareOption.type === '2'){
                      tt.redirectTo({
                        url:`/pages/litShare/litShare`
                      })
                    }
                  }
                 
                }else{
                 console.log('不是分享过来的')
                }
              },
              fail(res) {
                console.log(`save code 失败`);
              },
            });
          },
          fail(res) {
            console.log('Login fail', res)
          }
        })
      }
    })

    let globalData = getApp().globalData
    let secretKey = that.generateMixed(92)
    let url = `https://dvm05.btransmission.com/tp/tpAppGetToken.do`
    tt.request({
      'url': url,
      'data':{
        'appId':  globalData.appId,
        'secretKey': secretKey,
        'appVersion': globalData.mpVersion
      },
      success: function(res){
        console.log('验证返回值：', res.data)
        let data = String(res.data)
        that.setData({  isControl: data })
      },
      fail:function(err){
        console.log('验证返回值 fail：', err)
      }

    })
  },
  
  goNext(){
    let that = this
    let data = this.data.isControl
    let len = (data && data.length) || 0
    console.log('返回值长度=', len, 'data=', data)
    if(len === 16){ // yeah  shenheqi
      that.goNext2()
    }else{ // error
      that.goNext1()
    }
  
  },
  // 跳转首页
  goNext2(){
    let that = this
    console.log('this.hasSession=', this.data.hasSession)
    if(this.data.hasSession && this.data.userInfo){
      tt.switchTab({
        url:`/pages/home/<USER>/data`
      })
    }else{
      tt.getUserProfile({
        success: (res) => {
          tt.setStorage({
            key: "userInfo",
            data: res.userInfo,
            success(res) {
              console.log(`save userInfo`);
              tt.switchTab({
                url:`/pages/home/<USER>/data`
              })

            },
            fail(res) {
              console.log(`save userInfo失败`);
              tt.switchTab({
                url:`/pages/home/<USER>/data`
              })
            },
          }); 
        },
        fail: (res) => {
          console.log('tt.getUserProfile error=', res)
        },
      });

     

     

    }
  },
  // 跳转别的页面
  goNext1(){
    let that = this
    console.log('this.hasSession=', this.data.hasSession)
    if(this.data.hasSession && this.data.userInfo){
      if(that.shareOption){
        if(that.shareOption.ss === '1'){
            console.log('下一步， 还是 点击的别人的分享 ')
            tt.redirectTo({
              url:`/pages/index/index`
            })
        }else{
          if(that.shareOption.type === '1'){ // 大分享
            tt.redirectTo({
              url:`/pages/bigShare/bigShare`
            })
          }else if(that.shareOption.type === '2'){
            tt.redirectTo({
              url:`/pages/litShare/litShare`
            })
          }
        }
        
      }else{
        tt.redirectTo({
          url:`/pages/index/index`
        })
      }
     
    }else{
      tt.getUserProfile({
        success: (res) => {
          tt.setStorage({
            key: "userInfo",
            data: res.userInfo,
            success(res) {
              console.log(`save userInfo`);
              if(that.shareOption){
                if(that.shareOption.ss === '1'){
                  console.log('下一步， 还是 点击的别人的分享 ')
                  tt.redirectTo({
                    url:`/pages/index/index`
                  })
              }else{
                if(that.shareOption.type === '1'){ // 大分享
                  tt.redirectTo({
                    url:`/pages/bigShare/bigShare`
                  })
                }else if(that.shareOption.type === '2'){
                  tt.redirectTo({
                    url:`/pages/litShare/litShare`
                  })
                }

              }
              
              }else{
                tt.redirectTo({
                  url:`/pages/index/index`
                })
              }

            },
            fail(res) {
              console.log(`save userInfo失败`);
            },
          }); 
        },
        fail: (res) => {
          console.log('tt.getUserProfile error=', res)
        },
      });

     

     

    }
   
  },
  generateMixed(n) {
    var str = ['0','1','2','3','4','5','6','7','8','9',
    'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',
    'a','b','c','f','g','h','i','j','k','l','m','p','q','t','u','v','','x','y','z',
    // '@','#','$','¥','%','^','&','*','<','>','?','{','+','=','_','-'
    ];
    var res = "";
    let length = str.length - 1
    for(var i = 0; i < n+1 ; i ++) {
      var id = Math.ceil(Math.random()*length);
      res += str[id];
    }
    let insertStr = 'wonderss';
    let insertStrLen = insertStr.length;
    for(let j=0; j < insertStrLen; j++ ){
      let len =  parseInt(res.length / insertStrLen) 
      let startIndex = j * len;
      // let endIndex = (j+1) * len;
      let selectIndex = Math.round(Math.random()* len +startIndex); 
      let insertA = insertStr[j];
      res = `${ res.substring(0,selectIndex) }${ insertA }${ res.substring(selectIndex) }`;
    }
 
    return res;
 }

})