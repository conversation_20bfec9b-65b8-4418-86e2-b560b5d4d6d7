Page({
  data: {
    webview_url: ""
  },

  onLoad(option) {
    let shareback = option.shareback
    let oid = option.oid
    const allThis = this
    const jsonData = {}
    if(shareback === '1' || shareback === '2'){
      jsonData.shareback = shareback
      jsonData.oid = oid
    }
    tt.getStorage({
      key: "userInfo",
      success(res) {
        console.log('缓存取出 userInfo =', res)
        jsonData.userInfo = res.data
        tt.getStorage({
          key: "code",
          success(res) {
            console.log('缓存取出 code=', res)
            jsonData.code = res.data
            if(shareback === '1' || shareback === '2'){
              console.log('shareback的，不需要取 share数据')
              allThis.setUrl(jsonData)
            }else{
              tt.getStorage({
                key: "share",
                success(res) {
                  console.log('缓存取出 share =', res)
                  jsonData.share = res.data
                  allThis.setUrl(jsonData)
                },
                fail(res) {
                  console.log('缓存取出 share 失败 = ', res);
                  allThis.setUrl(jsonData)
                },
              })
            }
          
          },
          fail(res) {
            console.log('缓存取出 userInfo失败 = ', res);

          },
        });
      },
      fail(res) {
        console.log('缓存取出 userInfo失败 = ', res);
      },
    });
  
  },
  setUrl:function(data){
    let globalData = getApp().globalData
    console.log('globalData=', globalData)
    data.tgCode = globalData.tgCode
    data.appId = globalData.appId
    // data.tgCode = 'byteDance' // 微信的就传 'weChat'
    console.log('setUrl 里面的 jsonData', data)
    let src = globalData.webViewRoot + `?u=${ encodeURIComponent(JSON.stringify(data))  }`
    console.log('跳转的webview', src)
    this.setData({
      webview_url: src
    });
  },
  bindload(res) {
    console.log('网页已经加载完', res);
  },
  binderror(err) {
    console.log('页面加载失败', err);
  },
  bindmessage(e) {
    console.log('接收消息', e.detail.data, e.detail.data[0].msg);
  }

});
