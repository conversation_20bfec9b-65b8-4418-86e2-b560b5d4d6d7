import {auth} from '@/sys/auth'
import axios from 'axios'
export default function({url='',data={},method='post'}) {
	return new Promise((resolve, reject) => {
		uni.showLoading({
			title: '加载中'
		});
		auth.getGuestToken().then(token => {
			// console.log('token value', token)
			// console.log('request data', data)
			//axios实现
			axios.post(url, data).then(function (response) {
				// console.log('request success', response)
				resolve(response)
			}).catch(function (response) {
				// console.log('request', url, response)
				reject(response)
			}).finally(function () {
				uni.hideLoading()
			})

			// // uni.request实现
			// url = new RegExp('^http(s)?://').test(url) ? url : auth.webRoot + url // 判断时相对路径还是绝对路径，相对路径添加config.baseURL
			// console.log('request url', url)
			// uni.request({
			// 	url,
			// 	data,
			// 	method,
			// 	header: {
			// 		'Content-Type': 'application/x-www-form-urlencoded',
			// 		'accept': 'application/json, text/plain, */*',
			// 		'token': token
			// 	},
			// 	dataType: 'json',
			// 	withCredentials: true,
			// 	success: (response) => {
			// 		response = {
			// 			data: response.data,
			// 			status: response.statusCode,
			// 			errMsg: response.errMsg,
			// 			headers: response.header
			// 		};
			// 		if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.Token === 'string' && response.headers.Token.length > 0) {
			// 			response.headers.token = response.headers.Token
			// 			delete response.headers.Token
			// 		}
			// 		if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.token === 'string' && response.headers.token.length > 0) {
			// 			auth.saveToken(response.headers.token).then(() => {
			// 				resolve(response);
			// 			}).catch(reason => {
			// 				reject(reason)
			// 			})
			// 		} else if (response.status === 500 && response.data.indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') >= 0) { // token过期或非法使用
			// 			let reason
			// 			if (auth.verifyToken(auth.getToken()) !== 1) {
			// 				reason = 'IllegalAccessException: 未登陆或者登陆状态已过期！'
			// 				console.log('axios cleanToken', reason)
			// 				auth.cleanToken()
			// 			} else {
			// 				reason = 'IllegalAccessException: token权限与不符合接口注解要求！'
			// 				console.log(reason)
			// 			}
			// 			reject(reason)
			// 		} else {
			// 			resolve(response);
			// 		}
			// 	},
			// 	complete: () => {
			// 		uni.hideLoading()
			// 	},
			// })
		})
	})
}