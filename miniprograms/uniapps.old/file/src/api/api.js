import request from './request'
import {localStorage} from '@/sys/auth'

// #ifdef MP-TOUTIAO
const tgCode = 'byteDance'
// #endif
// #ifdef MP-WEIXIN
const tgCode = 'weChat'
// #endif

export function getCommonData () {
	let data = {'tgCode': tgCode}
	// #ifndef MP-TOUTIAO
	const accountInfo = uni.getAccountInfoSync();
	// console.log('accountInfo', accountInfo)
	data.appId = accountInfo.miniProgram.appId
	data.mpVersion = accountInfo.miniProgram.version
	data.envType = accountInfo.miniProgram.envVersion
	// #endif
	// #ifdef MP-TOUTIAO
	//获取appId没找到uni接口，直接用tt的
	const { microapp, common } = tt.getEnvInfoSync();
	// console.log("microapp:", microapp, "common:", common);
	data.appId = microapp.appId
	data.mpVersion = microapp.mpVersion
	data.envType = microapp.envType
	// #endif
	console.log('getCommonData data', data)
	return data
}

export function callLogin () {
	const data = getCommonData()
	console.log('getCommonData', data)
	return new Promise( (resolve, reject) => {
		uni.checkSession({
			success(res) {
				console.log("uni.checkSession true")
				data.code = localStorage.getItem('sessionCode')
				console.log("data.code", data.code)
				if(data.code != null) {
					console.log('code from localStorage', data.code)
					resolve(res)
				} else {
					return uniLogin(data).then(res => {
						console.log(' uniLogin(data)', res)
						resolve(res)
					}).catch(res => {
						console.log(' uniLogin(data) catch catch ', res)
						reject(res)
					})
				}
			},
			fail(res) {
				console.log("uni.checkSession false")
				return uniLogin(data).then(res => {
					console.log('fail uniLogin(data)  ', res)
					resolve(res)
				}).catch(res => {
					console.log('fail uniLogin(data) catch catch ', res)
					reject(res)
				})
			}
		})
	}).then(() => {
		console.log('callLogin then  ')
		return new Promise(resolve => {
			console.log('callLogin then  uni.getUserInfo ')
			const userInfo = uni.getStorageSync('userInfo');
			if(userInfo){
				resolve('success')
			}else{
				uni.getUserInfo({
					withCredentials: true,
					success(res) {
						console.log(`getUserInfo 调用成功`, res)
						data.userInfo = JSON.stringify(res.userInfo)

						uni.setStorageSync('userInfo', (res.userInfo));
						console.log("save userInfo1",  (res.userInfo))
						console.log("save userInfo2",  (data.userInfo))
						resolve('success')
					},
					fail(res) {
						console.log(`getUserInfo 调用失败`, res)
						resolve('fail')
					}
				})
			}
			
		}).then(() => {
			console.log('callLogin then  2  mpCodeLogin')
			return request({
				method: "GET",
				data: data,
				url: '/tp/mpCodeLogin.do',
			})
		})
	})
}
function uniLogin(data) {
	console.log('uniLogin')
	return new Promise((resolve, reject) => {
		wx.login({
			force: true,
			success(res) {
				console.log('login 调用成功 force=true', res);
				data.code = res.code
				if(data.code) {
					localStorage.setItem('sessionCode', data.code)
					console.log('login 调用成功', data.code)
					resolve(res)
				} else if (res.anonymousCode) {
					localStorage.setItem('sessionCode', res.anonymousCode)
					console.log('login 调用成功', res.anonymousCode)
					resolve(res)
				} else {
					console.log('login 调用失败，返回值错误！', res);
					reject(res)
				}
			},
			fail(res) {
				console.log('login 调用失败', res);
				reject(res)
			}
		})
	})
}
/*
 * creator: 2022-5-30 hxz
 * test 
 * params:  
*/ 
export function getTxt () {
	return request({
		method: "GET",
		url: '/tp/text.do',
	})
}
/*
 * creator: 2022-10-17 hxz
 *  获取初始文件夹 
 * params:  
*/ 
export function getWeChatHome () {
	return request({
		method: " POST",
		url: '/mpRes/getWeChatHome.do',
	})
}


/*
 * creator: 2022-10-17 hxz
 *  点击目录获取本级目录和子级目录的详细信息 
 * params:  
*/ 
export function getWeChatParentAndChildDir (data) {
	data = data || {}
	return request({
		method: " POST",
		url: '/mpRes/getWeChatParentAndChildDir.do',
		data: data
	})
}


 
/**
 * 获取子文件夹 和 子文件
 * @param folerid
 * @returns {AxiosPromise}
 */
export function getWxParentAndChildDir(folerid) {
	return request({
	  url: "/mpRes/getWeChatParentAndChildDir.do",
	  method: "post",
	  data: { category: folerid },
	});
  }
  
  /**
   * 搜索文件
   * @param searchtxt
   * @returns {AxiosPromise}
   * 入参说明：
   *  1 findName String 文件名字
   *  2 pageSize Integer 条数
   *  3 currentPageNo Integer 页码
   */
  export function getSearchFile(searchtxt) {
	return request({
	  url: "/mpRes/weChatFindFile.do",
	  method: "post",
	  data: { findName: searchtxt, pageSize: 200, currentPageNo: 1 },
	});
  }
  
  /**
   * 文件详情
   * @param searchtxt
   * @returns {AxiosPromise}
   */
  export function getFileDetails(fileID) {
	return request({
	  url: "/mpRes/getWeChatFileMes.do",
	  method: "post",
	  data: { file: fileID },
	});
  }
  

