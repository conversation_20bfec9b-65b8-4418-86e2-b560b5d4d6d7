@import "../uni.scss";

#app{
	font-size:$live-normal-txt-size;
	color:$uni-text-color;
	background-color: $uni-bg-color-grey;
}
.live-flex-panel{
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
	flex-grow: 0;
	flex-basis: auto;
	// align-items: stretch;
	align-content: flex-start;
}
.live-flex-item{
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}
.live-line{
	border-bottom: 1px solid #f1f1f1;
}
.live-link-btn{ 
	color: $live-button-link;
	cursor: pointer;
}
.uni-navigate-item {
    background-color: #FFFFFF; 
}
.uni-handle{
	margin-top:60rpx;
}  

.uni-panel{ 
	margin: 20rpx 30rpx;
	background: #fff;
	border-radius: 4rpx;
	padding: 20rpx;
	font-size: $live-normal-txt-size;
	line-height: 24px;
}
.live-bg-yellow{
	background-color:$live-bg-color-yellow!important ;
}
.live-txt-orange{
	color:$live-normal-txt-orange
}
.uni-panel-nobg{
	margin: 20rpx 30rpx;
	font-size: $live-normal-txt-size;
}
.uni-panel-all{ 
	background: #fff;
	line-height: 60rpx;
	padding: 20rpx 32rpx;
	font-size: $live-normal-txt-size;
}
.uni-font-lit{ 
	font-size: 24rpx;
}
.uni-navigate-icon {
    margin-left: 15rpx;
    color: #999999;
    font-size: 28rpx;
    font-weight: normal;
}
.uni-icon {
    font-family: uniicons;
    font-size: 24rpx;
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    display: inline-block;
    text-decoration: none;
    -webkit-font-smoothing: antialiased;
}

button.primary{
    color: #fff;
    background-color: $live-button-primary;
}
.uni-body{ 
	padding:20rpx 32rpx;
}


