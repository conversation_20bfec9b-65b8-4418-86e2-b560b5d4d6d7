{
    "name" : "live",
    "appid" : "__UNI__3C5E8F9",
    "description" : "直播助手",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "compatConfig" : {
        "MODE" : 2 // 2代表兼容Vue2模式，3代表非兼容Vue2模式
    },
    "app-plus" : {
        "usingComponents" : true,
        "compilerVersion" : 3,
        /* 5+App特有相关 */
        "modules" : {},
        "splashscreen": {
            "alwaysShowBeforeRender": false,
            "autoclose": false,
            "waiting": true,
            "delay": 0
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {}
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "wxb8859c1edcdb4a8e",
        "setting" : {
            "urlCheck" : true,
            "es6" : true,
            "postcss" : true,
            "minified" : true
        },
        "usingComponents" : true
    },
    "vueVersion" : "3",
    "mp-toutiao" : {
    }
}
