<template>
	<view>
		
	</view>
</template>

<script>
	import { callLogin, getTxt } from '../../api/api.js'
	import {auth, localStorage} from '@/sys/auth'

	export default {
		data() { 
			return {
				optionData: {}
			}
		},
		onLoad(option) {
			this.optionData = option
			console.log('optionData:', option)
			this.login(this)
			
		},
		methods: {
			login: (that) => {	
				console.log('this == that', this === that)
				callLogin().then(res => {
					console.log('login', res.data)
					let data = res.data
					if(data.success > 0) {
						console.log('登录成功！')
						let acc = auth.getAcc()
						console.log("acc", acc)
						let mobile = acc.mobile
						console.log("跳转 home ")
						uni.redirectTo({
							url: '/pages/home/<USER>'
						}); 
						 
					} else {
						let error = data.error
						console.log('登录失败！error msg', error)
						// console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
						switch (error.code) {
							case '2': //需要短信验证激活
								break
							case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号
								//	测试
								// that.getTxtFun()
								console.log('user', auth.getUser())
								// 跳转注册页 
								that.redirectPage()
							    break
							case '4': //可能是授权或者code问题，建议重试tt.login
								that.weixinLogin()
								// that.redirectPage()
								// that.ttLogin()
								break
							case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
								console.log('服务器问题，比如wonderss服务器访问微信/字节服务器异常')
								break
							case '255': // 功能 未实现
								that.redirectPage();
								break

						}
					}
				}).catch(res => {
					console.log('login error 但是 还让她跳转', res)
					that.redirectPage()
				})
			},
			getTxtFun:() => {
				getTxt().then(res => {
					console.log('txt response', res, res.data.data)
				}).catch(res =>{
					console.log('txt error', res)
				});
			},
			weixinLogin:() => {
				console.log('登录失败，需要wx.login重试！')
				wx.login({
					success (res) {
						console.log(`weixinLogin`, res);
						if (res.code) {
							console.log(`login 调用成功${res.code} ${res.anonymousCode}`);
						} else {
							console.log('wx.login 登录失败！' + res.errMsg)
						}
					}
				})
				// weixin.login({
				// 	force: true,
				// 	success(res) {
				// 		console.log(`login 调用成功${res.code} ${res.anonymousCode}`);
				// 	},
				// 	fail(res) {
				// 		console.log(`login 调用失败`);
				// 	},
				// });
			},
			
			redirectPage:()=>{ 
				uni.redirectTo({
					url: '/pages/home/<USER>'
				});
			}, 
			
		}
	}
</script>
<style>
</style>
