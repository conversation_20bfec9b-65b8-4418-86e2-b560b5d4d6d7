<template>
  <view class="home">
    <view class="logo">
      <text>Endress + <PERSON>user</text>
      <image src='../../static/images/logo.png' class='logoimg'></image>
    </view>
    <view v-if="search == 1">
      <view class="search-wrapper">
        <input
          placeholder="搜索常见问题"
          type="text"
          placeholder-class="search-placeholder"
          class="search-input"
          v-model="searchtxt"
        />
        <img src="../../static/images/search.png" class="search" />
        <text class="searchbtn" @click="searchFileBtn">搜索</text>
      </view>
    </view>
    <view v-else class="pfName">{{ pfName }}</view>
    <!-- folders -->
    <view class="conta" v-for="item in list" :key="item">
      <view class="folerItem" @click="getChild(item)">
        <i class="icon-folder"></i>
        <text class="ti">{{ item.name }}</text>
        <van-icon name="arrow" class="vicon" color="rgb(123 180 237)" />
      </view>
    </view>
    <!-- files -->
    <view class="conta" v-for="item in list2" :key="item">
      <view class="out" @click="goDetails(item)">
        <view class="kd">
          <text class="vicon"> > </text>
          <text class="ti">{{ item.name }}</text>
        </view>
      </view>
    </view>
  </view>

</template>

<script>
import { getWxParentAndChildDir, getSearchFile } from "../../api/api";

export default {
  data() {
    return {
      pfName: "",
      search: "",
      isLoad: true,
      list: [],
      list2: [],
    };
  }, 
  mounted() {
    const file = uni.getStorageSync('file');
    this.pfName = file.p; // 文件夹名字 或者 搜索的名字
    this.search = file.s; // 是否搜索
    let id = file.id
    if (this.search == 1) {
      this.searchtxt = this.pfName;
      this.searchFile();
    } else {
      this.getList(id);
    }
  },
  methods: {
    getChild(item) {
      let id = item.id;
      this.pfName = item.name;
      this.getList(id);
    },
    goDetails(item) {
      uni.setStorageSync('fileDetails', item);
      uni.navigateTo({
        url: '../fileDetails/fileDetails'
      }); 
    },
    getList(id) {
      this.isLoad = true;
      getWxParentAndChildDir(id).then((res1) => {
        let res = res1.data.data
        console.log(res);
        this.isLoad = false;
        let childFolder = res.childFolder;
        let filelist = res.list;
        if (childFolder && childFolder.length > 0) {
          this.list = childFolder;
          this.list2 = []; //list是文件夹，list2为文件夹里的内容
        } else if (filelist) {
          console.log(filelist);
          this.list = [];
          this.list2 = filelist || [];
        }
      });
    },
    searchFileBtn() {
      this.isLoad = true;
      getSearchFile(this.searchtxt).then((res) => {
        console.log(res);
        this.isLoad = false;
        let sechlist = res.list || [];
        this.list2 = sechlist;
        this.list = [];
      });
    },
    searchFile() {
      this.isLoad = true;
      getSearchFile(this.searchtxt).then((res) => {
        this.isLoad = false;

        console.log(res);
        let sechlist = res.list || [];
        this.list2 = sechlist;
        this.list = [];
      });
    },
  },
};
</script>
<style lang="scss">
.pfName{ 
  font-size: 22px;
  text-align: center;
  font-weight: bold;
  color:#333;
}
.conta {
  padding: 0 16px;
}

.search-wrapper {
  height: 40px;
  padding: 6px 15px;
  position: relative;
  margin-bottom: 15px;
  .search-input {
    width: 100%;
    height: 100%;
    padding-left: 40px;
    font-size: 14px;
    color: #666;
    box-sizing: border-box;
    border: 1px solid #ccc;
  }
  .search {
    position: absolute;
    width: 20px;
    height: 20px;
    left: 24px;
    top: 17px;
  }
  .search-placeholder {
    font-size: 14px;
    // font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #999999;
  }
  .searchbtn {
    position: absolute;
    top: 6px;
    right: 15px;
    background-color: #63a8c9;
    color: #fff;
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
  }
}

.folerItem {
  color: #333;
  padding: 10px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
  position: relative;
  .icon-folder {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url("../../static/images/folder.png") no-repeat;
    background-size: 100%;
    position: relative;
    top: 5px;
    margin-right: 16px;
  }
}
.logo {
  text-align: right;
  height: 66px;
  line-height: 66px;
  padding: 0 20px;
  text {
    font-size: 20px;
  }
  .logoimg {
    width: 38px;
    height: 28px;
  }
}
.vicon {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 16px;
  height: 16px;
  display: inline-block;
  // background: url("../assets/arrow.png");
  // background-size: 100%;
  // transform: rotate(180deg);
}
.out {
  border-left: 5px solid #61aad9;
  margin-top: 10px;
  box-shadow: 0 2px 2px #eee;
  .kd {
    border: 1px solid #eee;
    text-align: left;
    padding: 16px;
    position: relative;
    .ti {
      font-size: 14px;
    }
  }
}

 
</style>
