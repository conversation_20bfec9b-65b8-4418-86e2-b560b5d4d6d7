<template>
<view class="">
 
  <view class="logo">
    <text>Endress + Hauser</text>
    <image src='../../static/images/logo.png' class='logoimg'></image>
  </view> 
  <!-- search -->
  <view>
    <view class='search-wrapper'>
      <input placeholder="搜索常见问题" type="text" placeholder-class="search-placeholder" class="search-input" v-model="searchCon" />
      <image src='../../static/images/search.png' class='search'></image>
      <text class="searchbtn" @click='goSearch()'>搜索</text>
    </view>
  </view>
  <!-- 横向菜单 --> 
  <view v-show="!showAllPackes">
    <view style="position: relative;">
      <view class="toggleBtn" @click="toggleNav">
        <image src='../../static/images/left_arrow.png'></image>
      </view>
      <view class="nav">
        <view class="listcon">
          <text v-for="(item, index3) in list" :key="index3" :class="activeFolderID == item.id? 'active':''" class="listI"  @click='clickItem(item)'>
            <text class="title"> {{ item.name}} </text>
          </text>
        </view>
      </view>
    </view>
    <view class="packges">
      <swiper>
        <view v-for="(itemarr, index1) in childFolder" :key="index1" >
          <swiper-item>
            <view class="list" >
              <view class="pakI" v-for="(itemj, index2) in itemarr" :key="index2" >
                <view @click="gopage(itemj)" class="packcon"> 
                  <text>{{ itemj.name }}</text>
                </view>
              </view> 
            </view>
          </swiper-item>
        </view>
      </swiper>
      <view class="dots" v-hide="childFolder.length == 1">
        <block v-for="(itemarr2, index4) in childFolder" :key="index4">
          <view class="dot" :class="index4 == current ? ' active' : ''"></view>
        </block>
      </view>  

    </view>
  </view>
  <view v-show="showAllPackes">
    <view class="nav2">
      <text>为您提供以下分类</text>
      <view class="toggleBtn" @click="toggleNav()">
        <image src='../../static/images/left_arrow.png'></image>
      </view>
    </view>
    <view>
      <view class="list">
        <view v-for="(item, index2) in list" :key="index2" class="pakI" @click='clickItem(item)'>
          <view class="packcon"> 
            <text>{{ item.name}}</text>
          </view>
        </view>  
      </view>
    </view>
  </view>
  <view>    
  </view>

</view>

</template>

<script>
	import { getWeChatHome, getWeChatParentAndChildDir  } from '../../api/api.js'
  import {auth } from '@/sys/auth'
	export default {
		data() {
			return {
				baseURL:"https://wonderss.hongbeibz.com/",
				activeFolderID:"",
				showAllPackes:"",
				list:[],
				childFolder:[],
				searchCon:"",
			}
		},
		onLoad() {
			this.getFirstList()
		},
		methods: {
			getFirstList: function(){
				getWeChatHome().then(res1 => {
					var res = res1.data
          console.log("getWeChatHome,", res)
					if(res.data){
						let firstList = res.data.listFirstCategory || []
						let childs = res.data.childFolder || []
						let childFolder = []
						for(var i=0,len=childs.length;i<len;i+=6){
							childFolder.push(childs.slice(i,i+6));
						}  
						if(firstList.length > 0){
							this.activeFolderID = firstList[0]['id']
						} 
            this.list = firstList
            this.childFolder = childFolder
            console.log("childFolder", this.childFolder)
					}
				}).catch(res => {
					console.log("getList error", res)
				})
			},
			toggleNav: function () {
				this.showAllPackes = !this.showAllPackes
			},
			clickItem: function (info) {
				var that = this;
				this.showAllPackes = false
				this.activeFolderID = info.id 
				
				getWeChatParentAndChildDir({ 'category': Number(info.id) }).then(res1 => {
					console.log('点击目录获取本级目录和子级目录的详细信息 response', res1)
					let res = res1.data
					if(res.data){
						let childs = res.data.childFolder  
						let fileList = res.data.list  
						if(childs && childs.length > 0){
							let childFolder = []
							for(var i=0,len=childs.length;i<len;i+=6){
							childFolder.push(childs.slice(i,i+6));
							} 
							this.childFolder = childFolder

						}else{
							this.childFolder = []
							 
						}
					}

				}).catch(res =>{
					console.log('txt error', res)
				});
				 
				 
			},
			goSearch:function () {
				let p = this.searchCon
        uni.setStorageSync('file', { "s": 1, "p": p });
        uni.navigateTo({
          url: '../fileList/fileList'
        });

				// let link = this.baseURL + `vue/file/dist/index.html?p=${p}&s=1&token=${ auth.getToken() }`; 
				// uni.navigateTo({
				// 	url: '../webview/webview?link=' + encodeURIComponent(link)
				// })
			},
			gopage:function (item) {
        console.log(item)
				let id = item.id; 
				let p = item.name; 
        uni.setStorageSync('file', { "id": id, "p": p });
        uni.navigateTo({
          url: '../fileList/fileList'
        });
				// let link = this.baseURL +`vue/file/dist/index.html?id=${id}&p=${p}&token=${ auth.getToken() }`; 
				// uni.navigateTo({
        //   url: '../webview/webview?link=' + encodeURIComponent(link)
        // });
				
			},

		}
	}
</script>

<style lang="scss">
.logo{
  text-align: right;
  height: 66rpx;
  line-height: 66rpx;
  padding:0 20rpx;
}
.logo text{ font-size:44rpx; }
.logoimg{
  width: 76rpx;
  height: 50rpx;
}
.search-wrapper{
  height:70rpx;
  padding: 12rpx 26rpx;
  /* background: #960e0e; */
  position: relative;
  margin-bottom: 25rpx;
}
.search-input{
  width: 100%;
  height: 100%;
  /* background: #f6f6f6; */
  padding-left: 64rpx;
  font-size:24rpx;
  color: #666;
  box-sizing: border-box;
  border:1px solid #ccc; 
  /* border-radius:30rpx; */
}
.search{
  position: absolute;
  width: 35rpx;
  height: 35rpx;
  left: 40rpx;
  top: 25.29rpx;
}
.search-placeholder{
  font-size: 25.34rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #999999;
}
.scroll-item{
  box-shadow: 0 0 3px #ccc;
  float: left;
  background: #eee;
  width: 100rpx;
  height: 50rpx;
  margin-right: 10px;
}
.searchbtn{
  position: absolute;
  top:12rpx;
  right:27rpx;
  background-color: #63a8c9;
  color:#fff;
  height:70rpx;
  line-height: 70rpx;
  padding:0 40rpx;
  /* padding:14rpx 25rpx; */
  z-index: 99;
}
.xflow{
  margin:100rpx;
}

.nav{
  padding:30rpx;
  overflow-x: auto;
  position: relative; 
  box-shadow: 0 0 3px #ccc;
  white-space:nowrap;
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}
.nav .listcon{
  word-break: none;
}
.nav .active{
  color:#63a8c9; 
}
.nav .listI{
  padding:0 20px;
  border-right:1px solid #63a8c9; 
  font-size: 35rpx;
}
.toggleBtn{
  position: absolute;
  right:0;
  top:2rpx;
  background: #fff;
  width: 60rpx;
  height: 91rpx;
  text-align: center;
  background-color: #fff; z-index: 1;
}
.toggleBtn image{
  width: 40rpx;
  height: 40rpx;
  transform:rotate(-90deg);
  margin-top:25rpx;
}
.list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0;
  margin:0 20rpx;
}
.list .pakI {
  flex-grow: 0;
  overflow: hidden;
  width: 33%;
  height: 120rpx;
  position: relative;
  margin-bottom: 16rpx;
}
.list .pakI .packcon{
  display: flex;
  width: 90%;
  height: 98%;
  background-color: #E7ECF0;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 2rpx 2rpx #ccc;
  border-radius: 4px;
  align-items: center;
  flex-direction: row;
  position: relative;
  right: -7%;
}
.packcon text{
  display: block;
  color:#596a75;
  font-size: 20rpx;
  width: 100%;
  text-align: center;
}
.packges{ position: relative; }

swiper{
  height: 338rpx;
} 

.swiperContainer {
  position: relative;
}
.img {
 width: 100%;
 height: 100%
}
.imageCount {
  width:120rpx;
  height:50rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius:40rpx;
  line-height:50rpx;
  color:#fff;
  text-align:center;
  font-size:26rpx;
  position:absolute;
  left:13px;
  bottom:20rpx;
}
.dots{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20rpx;
  display: flex;
  justify-content: center;
}
.dots .dot{
  margin: 0 5rpx;
  width: 50rpx;
  height: 8rpx;
  background: #999;
  border-radius: 8rpx;
  transition: all .6s;
}
.dots .dot.active{
  width: 50rpx;
  background: #63a8c9;
}
.nav2{ 
  position: relative;
  height: 100rpx;
  padding: 0 40rpx;
  /* background: #ccc; */
  line-height: 100rpx;
}
.nav2 .toggleBtn{
  right: 40rpx;
}
.nav2 .toggleBtn image{ 
  transform:rotate(90deg); 
}




	
</style>
