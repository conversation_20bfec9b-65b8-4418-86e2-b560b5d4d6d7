<template>
  <view class="wheater"> 
    <view class="logo">
      <text>Endress + Hauser</text>
      <image src='../../static/images/logo.png' class='logoimg'></image>
    </view>
    <view class="h3">{{ info.name }}</view>
    <view class="bobo" v-for="item in contentList" :key="item">
      <view v-if="item.type == 1" v-html="item.content"></view>
      <view v-else-if="item.type == 2">
        <image
          :src="'https://wonderss.hongbeibz.com/upload/' + item.resEntity.path"
          :alt="item.title"
        />
        <view>{{ item.description }}</view>
      </view>
      <view v-else-if="item.type == 3">
        <video :src="'https://wonderss.hongbeibz.com/upload/' + item.resEntity.path"  controls></video>
           
        <!-- <video class="vdo" controls="controls">
          <source
            :src="
              'https://wonderss.hongbeibz.com/upload/' + item.resEntity.path
            "
            type="video/mp4"
          />
        </video> -->
        <view>{{ item.description }}</view>
      </view>
    </view>
    <view class="h1">Endress+Hauser China 版权所有</view>
  </view>
</template>

<style lang="scss" scoped>
.wheater {
  text-align: left;
  padding: 30px 16px;
  image,
  video {
    width: 100%;
  }
  .logo {
    text-align: right;
    height: 66px;
    line-height: 66px;
    padding: 0 20px;
    text {
      font-size: 20px;
    }
    .logoimg {
      display: inline-block;
      width: 38px;
      height: 28px;
    }
  }
  .h1 {
    font-size: 14px;
    margin-top: 75px;
    text-align: center;
  }
}
</style>

<script>
import { getFileDetails } from "../../api/api";
export default {
  data() {
    return {
      info: {},
      isLoad: true,
      contentList: {},
    };
  }, 
  mounted() {
    const fileDetails = uni.getStorageSync('fileDetails');
    let fileID = fileDetails.id;
    this.getfile(fileID);
  },
  methods: {
    getfile(fileID) {
      this.isLoad = true;
      getFileDetails(fileID).then((res1) => {
        let res = res1.data.data
        console.log(res);
        this.isLoad = false;
        this.contentList = res.listResAtt;
        this.info = res.res;
      });
    },
  },
};
</script>
