
<template>
	<view>
		<web-view :webview-styles="webviewStyles" :src="src"></web-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			webviewStyles: {
				progress: {
					color: '#FF3333'
				}
			},
			src:""
		}
	},
	onLoad: function (option) { //option为object类型，会序列化上个页面传递的参数
		console.log('option' , option); //打印出上个页面传递的参数。
		let url = decodeURIComponent(option.link)
		console.log("decodeURIComponent url :" ,  url)
		this.src = url
	}
}
 
</script>

