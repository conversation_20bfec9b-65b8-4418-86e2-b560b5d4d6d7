<script>
import {auth} from '@/sys/auth'
import { dd } from '@/utils/DateFormat.js'
export default {
	onLaunch: function () {
        auth.init()
        console.log('App Launch');
 
    },
    onShow: function() {
        console.log('App Show');
    },
	onHide: function() {
		console.log('App Hide');
	} 
};
</script>

<style lang="scss">
/*每个页面公共css */ 
@import "./common/common.scss";

.wating{
    width: 200px;
    height: 200px;
    margin:3px auto;
    margin: 100px auto 20px;
   
    background-repeat: no-repeat;
    opacity: 0.3;
}
.watTxt{
    text-align: center;
    line-height: 100px;
    font-size: 30px;
    opacity: 0.5;
} 
</style>
