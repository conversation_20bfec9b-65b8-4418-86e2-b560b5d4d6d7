import App from './App.vue'
import { dd } from '@/utils/DateFormat.js'
// #ifndef VUE3
import Vue from 'vue'
import GlobalComponents from './components'
// import 'amfe-flexible'

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
	...App
}) 

GlobalComponents(app)
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif
