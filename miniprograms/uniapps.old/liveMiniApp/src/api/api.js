import request from './request'
import {localStorage, auth} from '@/sys/auth'

// #ifdef MP-TOUTIAO
const tgCode = 'byteDance'
// #endif
// #ifdef MP-WEIXIN
const tgCode = 'weChat'
// #endif

export function getCommonData () {
	let data = {'tgCode': tgCode}
	// #ifndef MP-TOUTIAO
	const accountInfo = uni.getAccountInfoSync();
	// console.log('accountInfo', accountInfo)
	data.appId = accountInfo.miniProgram.appId
	data.mpVersion = accountInfo.miniProgram.version
	data.envType = accountInfo.miniProgram.envVersion
	// #endif
	// #ifdef MP-TOUTIAO
	//获取appId没找到uni接口，直接用tt的
	const { microapp, common } = tt.getEnvInfoSync();
	data.appId = microapp.appId
	data.mpVersion = microapp.mpVersion
	data.envType = microapp.envType
	// #endif
	return data
}

export function callLogin () {
	const data = getCommonData()
	return new Promise( (resolve, reject) => {
		uni.checkSession({
			success(res) {
				data.code = localStorage.getItem('sessionCode')
				if(data.code != null) {
					resolve(res)
				} else {
					return uniLogin(data).then(res => {
						resolve(res)
					}).catch(res => {
						reject(res)
					})
				}
			},
			fail(res) {
				return uniLogin(data).then(res => {
					resolve(res)
				}).catch(res => {
					reject(res)
				})
			}
		})
	}).then(() => {
		return new Promise(resolve => {
			const userInfo = uni.getStorageSync('userInfo');
			if(userInfo){
				resolve('success')
			}else{
				// uni.getUserProfile({
					// tt.getUserProfile
				uni.getUserInfo({
					withCredentials: true,
					success(res) {
						console.log('uni.getUserProfile success', res)
						data.userInfo = JSON.stringify(res.userInfo)

						uni.setStorageSync('userInfo', (res.userInfo));
						resolve('success')
					},
					fail(res) {
						// 拒绝授权
						console.log('uni.getUserProfile fail', res)
						resolve('fail')
					}
				})
			}
			
		}).then((res) => {
			console.log('fail 的情况 需要处理：', res)
			if(res === 'fail'){
				return res
			}else{
				return request({
					method: "GET",
					data: data,
					url: '/tp/mpCodeLogin.do',
				})
			}
		})
	})
}
function uniLogin(data) {
	return new Promise((resolve, reject) => {
		tt.login({
			force: true,
			success(res) {
				console.log('uniLogin success', res)
				data.code = res.code
				if(data.code) {
					localStorage.setItem('sessionCode', data.code)
					resolve(res)
				} else if (res.anonymousCode) {
					localStorage.setItem('sessionCode', res.anonymousCode)
					resolve(res)
				} else {
					reject(res)
				}
			},
			fail(res) {
				console.log('uniLogin fail', res)
				reject(res)
			}
		})
	})
}
/*
 * creator: 2022-5-30 hxz
 * test 
 * params:  
*/ 
export function getTxt () {
	return request({
		method: "GET",
		url: '/tp/text.do',
	})
}
/*
 * creator: 2022-5-27 hxz
 * 注册页 发送短信验证码 (不登录，且可以不是系统内手机号发短信)
 * params: phone + token
*/ 
export function sendMobileCode (data) {
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		method: "GET",
		url: '/auth/sendMessageVerificationCodeRegister.do',
		data: data
	})
}
 
/*
 * creator: 2022-5-27  hxz
 * 验证 验证码是否正确
 * params: phone verificationCode + token
*/ 
export function checkCode (data) {
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		method: "GET",  
		// url: '/auth/checkVerificationCode.do',  
		url: '/auth/verificationCodeMpLogin.do',  
    	data:data
	})
}

/*
 * creator: 2022-5-27  hxz
 * 验证 主播自行注册子机构接口 
 * params: phone 手机号 ，userName 抖音名 
*/ 
export function registerSonOrg (data) {
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		method: "GET", 
		url: '/org/registerSonOrg.do',  
    	data:data
	})
}


/*
 * creator: 2022-5-27  hxz
 * 从机构进入领地 调用的 换token接口
 * params: token 原 token
*/ 
export function updateTokenRemoveUser (data) {
	return request({
		method: "POST",  
		url: '/auth/updateTokenRemoveUser.do',  
    	// data:data
	})
}


/*
 * creator: 2022-5-27  hxz
 * 工作室成员列表接口 (1.210直播助手1)
 * params:    
*/ 
export function getTeamMembers (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		method: "POST",  
		url: '/org/getTeamMembers.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 加入他人工作室接口
 * params: phone 手机号 ，userName 抖音名，oid 加入的机构id
*/ 
export function joinTeam (data) {
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/org/joinTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 将成员移出团队 (
 * params: outUserId 要移出的用户id 
*/ 
export function memberOutTeam (data) {
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/org/memberOutTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 工作室已移出团队的成员列表接口
 * params:  
*/ 
export function getOutTeamMembers (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/org/getOutTeamMembers.do',  
    	data:data
	})
}
/*
 * creator: 2022-8-16  hxz
 * 将成员重新移入团队 
 * params:  
*/ 
export function memberBackTeam (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/org/memberBackTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 成员退出团队接口 
 * params:  
*/ 
export function activeMemberOutTeam (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/org/activeMemberOutTeam.do',  
	})
}

/*
 * creator: 2022-8-16  hxz
 * 注销工作室接口  
 * params:  
*/ 
export function cancellationTeam (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/org/cancellationTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 注销账号  
 * params:  
*/ 
export function closeAcc (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
    return new Promise((resolve, reject) => {
        request({
            url: '/auth/closeAcc.do',
            data: data
        }).then(response => {
			resolve(response)
		}).catch( response => {
			if(response.startsWith('token check error')) {
				let list = response.split('\0')
				if (list instanceof Array && list.length === 2) {
					clearTimeout(list[1])
					response = {status:200,data:{success:1,data:'操作成功！ \n本账号已成功注销！'}}
					resolve(response)
				}
			}
			reject(response)
        })
    })
}

/*
 * creator: 2022-8-16  hxz
 *  领地 用户信息  
 * params:  
*/ 
export function getTerritoryAuthInfo (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/authView/getTerritoryAuthInfo.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 *  领地 手机号修改记录
 * params:  
*/ 
export function getAccEditHistories (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/auth/getAccEditHistories.do',  
    	data:data
	})
}

// 
/*
 * creator: 2022-8-16  hxz
 *  领地 获取头像图片展示的跟路径
 * params:  
*/ 
export function getRootPathFile () {
	return request({
		url: '/uploads/getRootPath.do',  
	})
}
/*
 * creator: 2022-8-16  hxz
 *   机构列表 
 * params:  
*/ 
export function organizationList (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/appManage/organizationList.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 *   登录某个工作室 
 * params:   mobile， oid
 * res: user{"userName"姓名 ,"roleCode",}
*/ 
export function sureLogin (data) {
	data = data || {}
	const commonData = getCommonData()
	data.tgCode = commonData.tgCode
	data.appId = commonData.appId
	return request({
		url: '/sys/sureLogin.do',  
    	data:data
	})
}


/*
 * creator: 2022-9-27  hxz
 *   分享接口 
 * params:   
1 subOrg integer 当前登录的机构id
2 accId Integer 与小程序关联的一个id
3 type String 分享类型 1是注册 2是成员
4 module String 抖音这里传“ assistant ”，后续值在继续补充
 * res: 
1 tpAppShare 分享的详细信息
    1.1 id 分享的id 此值要回传
    1.2 type 分享的类型 1是注册 2是成员
    1.3 subOrg 子机构的id 此值也要回传 用途是在注册成员时得知道该机构是否已经有这个成员了 所以得传一下
    1.4 title 标题 可以用我返回的也可以自己写
    1.5 content 内容 可以用我返回的也可以自己写
    1.6 imagePath 图片路径 这里暂时还不知道咋处理等做完后在讨论吧
*/ 
export function ttAassistantSharing (data) {
	// data = data || {}
	// const commonData = getCommonData()
	// data.tgCode = commonData.tgCode
	// data.appId = commonData.appId
	return request({
		// url: '/plarform/ttAassistantSharing.do',  
		url: '/mpa/getTtShareMes.do',  
    	// data: data
	})
}



