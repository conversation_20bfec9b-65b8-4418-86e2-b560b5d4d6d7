<script>
// import {auth, localStorage} from '@/sys/auth'
export default {
	onLaunch: function (option) {
        // localStorage.clear()
        // auth.init()
        console.log('App Launch');
    },
    onShow: function(option) {
        // 隐藏原生tabbar
		// uni.hideTabBar({});

    },
	onHide: function() {
		console.log('App Hide');
	} 
};
</script>

<style lang="scss">
/*每个页面公共css */ 
@import "./common/common.scss";

.wating{
    width: 200px;
    height: 200px;
    margin:3px auto;
    margin: 100px auto 20px;
   
    background-repeat: no-repeat;
    opacity: 0.3;
}
.watTxt{
    text-align: center;
    line-height: 100px;
    font-size: 30px;
    opacity: 0.5;
} 
</style>
