import packageJson from '@/../package.json'
import { Base64 } from 'js-base64'
import axios from 'axios'
import Cookies from 'js-cookie'
import qs from 'qs'
import {baseRoot} from "../../config"
const auth = {
  getToken () {
    let token = localStorage.getItem('token')
    if (this.verifyToken(token)) {
      return token
    } else if ((token = this.getCookie('token')) && this.verifyToken(token)) {
      return token
    } else {
      return null
    }
  },
  saveToken (token) {
    return new Promise((resolve, reject) => {
      let verify = this.verifyToken(token)
      if (verify === 1) {
        localStorage.setItem('token', token)
        axios.defaults.headers.common['token'] = token
        if (typeof this.http !== 'undefined') {
          this.http.headers.common['token'] = token
        }
        let acc
        if ((acc = this.getAcc()) !== null && acc.id >= 0) {
          this.setCookie('accId', acc.id)
          this.setCookie('mobile', acc.mobile)
        }
        this.refreshUser().then(() => {
          // console.log('saveToken refreshUser success')
          resolve('success')
        }).catch(reason => {
          reject(reason)
        })
      } else if (verify === -1) {
        this.cleanToken()
        reject('token check error')
      }
    })
  },
  cleanToken () {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('org')
    this.removeCookie('accId')
    this.removeCookie('mobile')
    this.removeCookie('userID')
    this.removeCookie('oid')
    delete axios.defaults.headers.common['token']
    if (typeof this.http !== 'undefined') {
      delete this.http.headers.common['token']
    }
    console.log('token cleand')
  },
  getGuestToken() {
    return new Promise((resolve, reject) => {
      let result = this.getToken()
      if (this.verifyToken(result) === 1) {
        resolve(result)
      } else {
        if(typeof result === 'string' && result.length>0) {
          console.log('getGuestToken verifyToken error', result)
        }
        this.getUrl(this.webRoot + '/auth/getGuestToken.do').then(result => {
          if (typeof result === 'string' && result.length > 0) {
            this.saveToken(result)
          } else {
            console.log('getGuestToken error', result)
            result = null
          }
          resolve(result)
        }).catch(reason => {
          console.log('getGuestToken error', url, res)
          reject(reason)
        })
      }
    })
  },
  getDevToken({accId, userId}) {
    return new Promise((resolve, reject) => {
      let url = this.webRoot + '/auth/getDevToken.do'
      let params = []
      if (typeof accId !== 'undefined') {
        params.push('accId=' + accId)
      }
      if (typeof userId !== 'undefined') {
        params.push('userId=' + userId)
      }
      if (params.length > 0) {
        url += '?' + params.join('&')
      }
      this.getUrl(url).then(token => {
        if (typeof token === 'string' && token.length > 0) {
          this.saveToken(token)
          if (this.isLogInOrg()) {
            setTimeout(function () { auth.getUser() }, 0)
          }
          resolve(token)
        } else {
          console.log('getDevToken return null')
          reject('error')
        }
      }).catch(reason => {
        console.log('getDevToken error', reason)
        reject(reason)
      })
    })
  },
  refreshCurrentUserOrg() {
    return new Promise((resolve, reject) => {
      // console.log('this.webRoot:', this.webRoot)
      this.getUrl(this.webRoot + '/sys/refreshCurrentUserOrg.do').then(result => {
        let obj, data;
        if ((typeof result === 'string' && result.length > 0 && (obj = JSON.parse(result)) != null
            || typeof result === 'object' && (obj = result) != null)
            && (data = obj.data) != null) {
          let user = data.user
          // console.log('refreshCurrentUserOrg user', user)
          if (user != null) {
            localStorage.setItem('user', user)
            // console.log('refreshCurrentUserOrg localStorage.setItem', user)
            this.setCookie('userID', JSON.parse(user).userID)
          }
          let org = data.org
          if (org != null) {
            localStorage.setItem('org', org)
            this.setCookie('oid', JSON.parse(org).id)
          }
          resolve('success')
        }
      }).catch(reason => {
        // console.log('refreshCurrentUserOrg error', reason)
        reject(reason)
      })
    })
  },
  getTokenPayload (token) {
    if (typeof token !== 'string') {
      token = this.getToken()
    }
    if (typeof token === 'string' && token.length > 147 && token.length < 1024) {
      let tokenArr = token.split('.')
      if (typeof tokenArr === 'object' && tokenArr instanceof Array && tokenArr.length === 3) {
        return Base64.decode(tokenArr[1])
      }
    }
    return null
  },
  getByName (name, token) {
    let tokenStr = this.getTokenPayload(token)
    if (typeof tokenStr === 'string') {
      let tokenObj = JSON.parse(tokenStr)
      if (typeof tokenObj === 'object') {
        return tokenObj[name]
      }
    }
    return null
  },
  getExp (token) {
    return this.getByName('exp', token)
  },
  getSessionid () {
    return this.getByName('sessionid')
  },
  getAccId () {
    let acc = this.getAcc()
    if (typeof acc === 'object') {
      return acc.id
    } else {
      return null
    }
  },
  getAcc () {
    return this.getByName('acc')
  },
  getUserID () {
    return this.getByName('userID')
  },
  getUser () {
    let user
    if (localStorage.getItem('user') !== null && this.getUserID() === (user = JSON.parse(localStorage.getItem('user'))).userID) {
      return user
    } else {
      return null
    }
  },
  refreshUser () {
    return new Promise(resolve => {
      if (this.getUserID() != null) {
        this.refreshCurrentUserOrg().then(() => {
          // console.log('refreshUser refreshCurrentUserOrg then')
          resolve('success')
        }).catch(() => {
          resolve('error')
        })
        // if (localStorage.getItem('user') != null) {
        //   console.log('user4', localStorage.getItem('user'))
        //   return JSON.parse(localStorage.getItem('user'))
        // }
      } else {
        localStorage.removeItem('user')
        localStorage.removeItem('org')
        resolve('remove')
      }
    })
  },
  getOrg () {
    if (localStorage.getItem('org') != null) {
      return JSON.parse(localStorage.getItem('org'))
    } else if (this.getByName('userID') != null) {
      this.refreshCurrentUserOrg()
      if (localStorage.getItem('org') != null) {
        return JSON.parse(localStorage.getItem('org'))
      }
    }
    return null
  },
  getPackageName () {
    return packageJson.name??''
  },
  getCookie (name) { // 获取cookie，仅在同域名iframe中可用
    return Cookies.get(name)
  },
  setCookie (name, value) { // 设置特定的过期时间和路径，给EFK采集数据用
    Cookies.set(name, value, {expires: 1 / 6, path: '/'})
  },
  removeCookie (name) {
    Cookies.remove(name, {path: '/'})
  },
  getUrl(url) {
    return new Promise(((resolve, reject) => {
      axios({
        url: url,
      }).then(res => {
        let result = res.data
        if(typeof result == 'undefined') {
          // console.log('getUrl response error', url, res)
          reject('error')
        } else {
          resolve(result)
        }
      }).catch(res => {
        // console.log('getUrl error', url, res)
        reject('error')
      });
    }))
    // return new Promise((resolve, reject) => {
    //   uni.request({
    //     url,
    //     header: {
    //       'Content-Type': 'application/x-www-form-urlencoded',
    //       'accept': 'application/json, text/plain, */*',
    //       'token': this.getToken()
    //     },
    //     dataType: 'json',
    //     withCredentials: true,
    //     success: (response) => {
    //       response = {
    //         data: response.data,
    //         status: response.statusCode,
    //         errMsg: response.errMsg,
    //         headers: response.header
    //       };
    //       if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.Token === 'string' && response.headers.Token.length > 0) {
    //         response.headers.token = response.headers.Token
    //         delete response.headers.Token
    //       }
    //       if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.token === 'string' && response.headers.token.length > 0) {
    //         auth.saveToken(response.headers.token).then(() => {
    //           let result = response.data
    //           if(typeof result == 'undefined') {
    //             console.log('getUrl response error', url, res)
    //             reject('error')
    //           } else {
    //             resolve(result)
    //           }
    //         }).catch(reason => {
    //           reject(reason)
    //         })
    //       } else if (response.status === 500 && response.data.indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') >= 0) { // token过期或非法使用
    //         let reason
    //         if (auth.verifyToken(auth.getToken()) !== 1) {
    //           reason = 'IllegalAccessException: 未登陆或者登陆状态已过期！'
    //           console.log('axios cleanToken', reason)
    //           auth.cleanToken()
    //         } else {
    //           reason = 'IllegalAccessException: token权限与不符合接口注解要求！'
    //           console.log(reason)
    //         }
    //         reject(reason)
    //       } else {
    //         let result = response.data
    //         if(typeof result == 'undefined') {
    //           console.log('getUrl response error', url, res)
    //           reject('error')
    //         } else {
    //           resolve(result)
    //         }
    //       }
    //     },
    //     fail(res) {
    //       console.log('getUrl error', url, res)
    //       reject('error')
    //     }
    //   })
    // })
  },
  getDomain (url) {
    let index
    if (typeof url !== 'string' || url.length <= 0) {
      url = location.href
    }
    if ((index = url.indexOf('//')) >= 0) {
      url = url.substring(index + 2)
    }
    if ((index = url.indexOf('/vue/')) > 0) {
      return url.substring(0, index)
    } else if ((index = url.indexOf('/')) > 0) {
      return url.substring(0, index)
    } else {
      return url
    }
  },
  getPureDomain (url) {
    let domain = this.getDomain(url)
    if (typeof domain === 'string') {
      let index
      if ((index = domain.indexOf(':')) > 0) {
        return domain.substring(0, index)
      }
    }
    return domain
  },
  getPath (url) {
    let index
    if (typeof url !== 'string' || url.length <= 0) {
      url = location.href
    }
    if ((index = url.indexOf('//')) >= 0) {
      url = url.substring(index + 2)
      if ((index = url.indexOf('/')) > 0) {
        url = url.substring(index)
      } else {
        return '/'
      }
    }
    if (url.length <= 1) {
      return '/'
    } else if ((index = url.indexOf('/vue/')) > 0) {
      return url.substring(index)
    } else if ((index = url.indexOf('/')) > 0) {
      return url.substring(index + 1)
    } else {
      return url
    }
  },
  verifyToken (token) {
    // typeof token === 'string' && console.log('verifyToken', typeof token === 'string' && token.length > 147 && token.length < 1024 && token.split('.').length === 3, this.getExp(token) * 1000, Date.now().valueOf(), token.split('.').length)
    if (typeof token === 'string' && token.length > 147 && token.length < 1024 && token.split('.').length === 3) {
      if (this.getExp(token) * 1000 > Date.now().valueOf()) {
        return 1
      } else {
        return -1
      }
    } else {
      return 0
    }
  },
  isDevDomain (domain) {
    const dev_domains = ['wonderss.hongbeibz.com', 'dvm01.btransmission.com', 'dvm02.btransmission.com', 'dvm03.btransmission.com', 'dvm04.btransmission.com', 'dvm05.btransmission.com']
    let patternIpPort = /(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d):\d{1,5}/
    return dev_domains.indexOf(domain) > -1 || domain.toLowerCase().includes('frp.btransmission.com') || domain.toLowerCase().includes('localhost') || patternIpPort.test(domain)
  },
  init () {
    auth.cleanToken()
    // localStorage.clear()
    let webRoot = baseRoot
    if(webRoot.endsWith('/'))
      webRoot = webRoot.slice(0, -1)
    this.webRoot = webRoot
    // console.log('auth.webRoot', this.webRoot)
    // isGuest && this.getGuestToken()

    // 适配小程序网络请求
    axios.defaults.timeout = 120000
    axios.defaults.adapter = function (config) {
      return new Promise((resolve, reject) => {
        var settle = require('axios/lib/core/settle');
        var buildURL = require('axios/lib/helpers/buildURL');
        const url = new RegExp('^http(s)?://').test(config.url) ? config.url : webRoot + config.url // 判断时相对路径还是绝对路径，相对路径添加config.baseURL
        // console.log('axios.defaults.adapter url', url)
        uni.request({
          method: config.method.toUpperCase(),
          url: buildURL(url, config.params, config.paramsSerializer),
          header: config.headers,
          data: config.data,
          dataType: config.dataType,
          responseType: config.responseType,
          sslVerify: config.sslVerify,
          complete: function complete(response) {
            // console.log('axios.defaults.adapter complete1', response)
            response = {
              data: response.data,
              status: response.statusCode,
              errMsg: response.errMsg,
              headers: response.header,
              config: config
            };
            if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.Token === 'string' && response.headers.Token.length > 0) {
              response.headers.token = response.headers.Token
              delete response.headers.Token
            }
            if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.token === 'string' && response.headers.token.length > 0) {
              auth.saveToken(response.headers.token).then(() => {
                // console.log('axios saveToken', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
                settle(resolve, reject, response);
              }).catch(reason => {
                reject(reason)
              })
            } else if (response.status === 500 && response.data.indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') >= 0) { // token过期或非法使用
              let reason
              if (auth.verifyToken(auth.getToken()) !== 1) {
                reason = 'IllegalAccessException: 未登陆或者登陆状态已过期！'
                // console.log('axios cleanToken', reason)
                auth.cleanToken()
                reject('token check error')
              } else {
                reason = 'IllegalAccessException: token权限与不符合接口注解要求！'
                // console.log(reason)
              }
              reject(reason)
            } else {
              settle(resolve, reject, response);
            }
          }
        })
      })
    }
    axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8'
    axios.defaults.headers['Wndrss-Prj'] = this.getPackageName()
    axios.defaults.withCredentials = true
    axios.defaults.transformRequest = [function (data) {
      return qs.stringify(data, { indices: false })
    }]
    // axios.interceptors.response.use(response => {
    //   // if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.token === 'string' && response.headers.token.length > 0) {
    //   //   this.saveToken(response.headers.token)
    //   //   console.log('axios saveToken', this.getByName('tpMemberId', this.getToken()), this.getUserID())
    //   // }
    //   console.log('axios.interceptors.response.use',response)
    //   return response
    // }, error => {
    //   let response = error.response
    //   if (response.status === 500 && response.data.indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') >= 0) { // token过期或非法使用
    //     if(this.verifyToken(this.getToken()) !==1 ) {
    //       console.log('axios cleanToken', 'IllegalAccessException: 未登陆或者登陆状态已过期！')
    //       this.cleanToken()
    //     } else {
    //       console.log("IllegalAccessException: token权限与不符合接口注解要求！")
    //     }
    //   }
    //   return Promise.reject(error)
    // })
  }
}
const localStorage = {
  getItem(key) {
    let result = false
    try {
      result = uni.getStorageSync(key)
      if (result === '') {//uni.getStorageSync，key不存在返回空字符，因此当返回空字符串的时候，需要检查key是否存在。
        const res = uni.getStorageInfoSync();
        if(res.keys.indexOf(key)<0) {
          result = null
        }
      }
    } catch (e) {
      console.log('uni.getStorageSync error', e)
    }
    return result
  },
  setItem(key, value) {
    try {
      uni.setStorageSync(key, value)
    } catch (e) {
      console.log('uni.setStorageSync error', e)
    }
  },
  removeItem(key) {
    try {
      uni.removeStorageSync(key)
    } catch (e) {
      console.log('uni.removeStorageSync error', e)
    }
  },
  clear() {
    try {
      uni.clearStorageSync()
    } catch (e) {
      console.log('uni.clearStorageSync error', e)
    }
  }
}
export {auth, localStorage}