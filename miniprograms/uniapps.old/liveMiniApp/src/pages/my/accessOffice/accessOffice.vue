<template>
	<view class="uni-container">
		<view class="uni-panel-all" >
			<text>当前，账号XXXXXXXXXXX可登录的机构有如下XX个</text>
		</view>
		<view class="officeList">
			<liveTable :columns="columns" :list="list"></liveTable> 
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				columns: [{
						title: "机构简称",
						key: "name",
						flex:1
					},{
						title: "录入至该机构的时间",
						key: "time",
						flex:3
					}
					],
					list:[
						{
							name:"11111",
							time:"2020-02-02 12:12:12"
						},
						{
							name:"11111",
							time:"2020-02-02 12:12:12"
						},
						{
							name:"11111",
							time:"2020-02-02 12:12:12"
						},
						{
							name:"11111",
							time:"2020-02-02 12:12:12"
						}
					]
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss">
.uni-panel-all>view:nth-child(odd){
	background:#fff;
}
</style>
