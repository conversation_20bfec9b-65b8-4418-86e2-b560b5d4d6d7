<template>
	<view class="uni-container">
		<view class="uni-panel-all live-flex-panel live-flex-item">
			<view class="img">
				<image class="image" :src="userDetails.avatar ? (fileUrl + userDetails.avatar) : userImgErr" mode="头像"></image>
			</view>
			<view class="user">
				<view class="officeName">{{ userDetails.name }}的领地</view>
				<view class="userInfo">
					<view @click="goPage(accountDetails)">
						<text class="txt">账号：{{userDetails.mobile }}</text>
						<text class="uni-navigate-icon uni-icon">></text>
					</view>
				</view>
			</view>
		</view>
		 
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item linehei live-line" @click="goPage('')">
				<text class="uni-navigate-text">修改账号</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage('')">
				<text class="uni-navigate-text">关于</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		<view class="uni-panel-all mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item " @click="registOffice">
				<text class="uni-navigate-text">注册工作室</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view> 
		</view>
		<view class="uni-panel-all mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item " @click="goPage('')">
				<text class="uni-navigate-text">我的收益</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view> 
		</view>
		
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item linehei live-line" @click="goPage('officeList')">
				<text class="uni-navigate-text">返回机构</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="cancelAcc">
				<text class="uni-navigate-text">注销账号</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		
		<tabBar :activeIndex="5" :isShowData="true" :mainTab="2"></tabBar>	
	</view>
</template>

<script>

import {auth} from "../../../../sys/auth.js";
import userImgErr from "../../../../static/img/tabbar/1.png";
import { updateTokenRemoveUser, getTerritoryAuthInfo, getRootPathFile, sureLogin, closeAcc } from "@/api/api.js";
	export default {
		data() {
			return {
				accountDetails: "/pages/my/userDetails/userDetails",
				userDetails:"",
				userImg : '',
				fileUrl: '',
				officeList: uni.getStorageSync('officeList') || [],
				officeItem: uni.getStorageSync('officeInfo'),

			}
		},
		onLoad(option) {
			let liveNum = 0
			this.officeList.forEach(offItem => {
				if(offItem.code == 'liveHelper'){
					liveNum++;
				}
			})
			console.log('liveNum=', liveNum)
			console.log('officeItem=', this.officeItem)
			if(
				(liveNum > 0 && this.officeItem && this.officeItem.id)
			){
				this.changeToken();
			}else{
				this.getUserDetails();
			}
			
			this.userInfo = uni.getStorageSync('userInfo');
			this.userImg = this.userInfo.avatarUrl || userImgErr 
			this.getFileRoot()
		},
		methods: {
			closeAccFun:function(){
				closeAcc().then((res)=>{
					console.log('closeAcc = ' , res)
					let dda = res.data
					if(dda.success === 2){
						let txt = dda.data
						console.log('txt', txt)
						uni.showToast({
							title: txt,
							icon: 'none',
							duration: 3000
						})
					} else if(dda.success === 1){
						uni.showToast({
							title: '操作成功，本账号已成功注销！',
							icon: 'none',
							duration: 3000,
							success:function(){
								setTimeout(() => { 
									uni.navigateTo({
										url: '/pages/startPage/startPage'
									})
								}, 3000)
							}
						})
					}
				})
			},
			cancelAcc:function(){ // 注销账号
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定后，该账号将无法再登录“字幕精灵”。确定注销吗？',
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定');
							that.closeAccFun()
							
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			},
			registOffice:function(){ // 注册机构
				let officeList = uni.getStorageSync('officeList') || [];
				console.log('officeList', officeList)
				let registed = false
				officeList.forEach((itemOffice) => {
					let phone = itemOffice.itemOffice
					let code = itemOffice.code 
					console.log(phone, code)
					if(code === "liveHelper" && phone === this.userDetails.mobile){
						console.log('true 了哈')
						registed = true
					}
				})
				if(registed){
					uni.showToast({
						title: '操作失败！因为该账号已注册工作室了！',
						duration: 2000,
						icon:"none"
					});
				}else{
					let url = '/pages/register/register-3/register-3'
					uni.navigateTo({
						url:url
					})
				}
				
			},
			getFileRoot:function(){
				getRootPathFile().then((res) =>{
					console.log('getRootPathFile =' , res.data)
					this.fileUrl = res.data.fileUrl
					console.log('this.rootPath ', this.rootPath )
				})
			},
			goPage:function(url){	
				if(url.length > 0){
					if(url === 'officeList'){ // 返回机构
						url = '/pages/officeList/officeList'
						let userPhone = this.userDetails.mobile ;
						console.log('返回机构', )
						console.log('userPhone', userPhone)
						console.log('officeItem', this.officeItem)
						if(userPhone && this.officeItem && this.officeItem.id){
							sureLogin({ "mobile": userPhone, "oid": this.officeItem.id }).then((res)=>{
								console.log('sureLogin', res)
								let userInfo = uni.getStorageSync('userInfo');
								let userLoginDto = res.data.userLoginDto
								let userAuth = res.data.user

								userInfo.orgU = userLoginDto
								userInfo.orgUserAuth = userAuth
								uni.setStorageSync('userInfo', userInfo);
								console.log('userInfo', userInfo)

								let roleCode = userLoginDto.roleCode
								if(roleCode){

								}else{

								}

								uni.redirectTo({ 
									// url: '/pages/home/<USER>/data' ,
									url: '/pages/redirectpage/redirectpage?path=/pages/home/<USER>/data',
									// url: "/pages/webview/webview?link=" + encodeURIComponent(link),
								}); 
							})

						}else{
							uni.showToast({
								title:"暂无可以返回的机构！",
								icon: 'none',
								duration: 1000
							})
						}
					}else{
						uni.navigateTo({
							url:url
						})
					}

					
				}else{
					uni.showToast({
						title:"功能未开放，敬请期待！",
							icon: 'none',
							duration: 1000
					})
				}
				
			},
			changeToken(){
				updateTokenRemoveUser().then((res)=>{
					console.log('res = ', res)
					if(res.data.length  > 0){
						this.getUserDetails();
					}

				})
			},
			getUserDetails(){
				getTerritoryAuthInfo().then((res) => {
					console.log('getTerritoryAuthInfo =', res)
					this.userDetails = res.data.data 
					uni.setStorageSync('privateUser', this.userDetails ); 
				}).catch((res)=>{
					console.log('bug了，还是换一token下吧')
					console.log(res)
					this.changeToken();
				})
			},

		}
	}
</script>

<style lang="scss">
	.linehei{
		line-height: 60rpx;
	}
	.mar30 { 
		margin-top: 30rpx;
	}
	.user{
		width: 75%;
		.txt{
			font-size: 0.9em;
		} 
		.uni-icon{
			float: right;
			position: relative;
			top:16rpx;
		}
	}
	.img{ 
		width:120rpx; 
		height: 120rpx;
		overflow: hidden;
		background-color: #ddd;
		border-radius: 60rpx;
		.image{
			width:120rpx;
			height:120rpx;
		}
	}
	
</style>
