<template>
	<view class="uni-container">
		<view class="wating icon-manage1">  </view>
			<view class="watTxt">敬请期待</view>
		
		<tabBar :activeIndex="3" :isShowData="true" :mainTab="2"></tabBar>	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>

<style lang="scss">
	.icon-manage1{ background: url("../../../../static/img/tabbar/manage1.png");   background-size: 200px; } 

	
</style>
