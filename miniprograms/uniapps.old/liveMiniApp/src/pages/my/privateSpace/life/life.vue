<template>
	<view class="uni-container">
			<view class="wating icon-life">  </view>
			<view class="watTxt">敬请期待</view>
		<tabBar :activeIndex="1" :isShowData="true" :mainTab="2"></tabBar>	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>

<style lang="scss">
.icon-life{ background: url("../../../../static/img/tabbar/life.png");   background-size: 200px; } 
	
</style>
