<template>
	<view class="uni-container">
		<view class="wating icon-mailList">  </view>
			<view class="watTxt">敬请期待</view>
		
		<tabBar :activeIndex="2" :isShowData="true" :mainTab="2"></tabBar>	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>


<style lang="scss">
.icon-mailList{ background: url("../../../../static/img/tabbar/mailList.png");   background-size: 200px; } 
	
	
</style>