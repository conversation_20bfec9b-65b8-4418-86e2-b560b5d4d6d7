<template>
	<view class="uni-container">
		<view class="uni-panel-all live-flex-panel live-flex-item">
			<view class="img">
				<image class="image" src="../../../static/img/tabbar/guanzhuactive.png" mode="头像"></image>
			</view>
			<view class="user">
				<view class="officeName">抖音名的领地</view>
				<view class="userInfo">
					<view @click="goPage(accountDetails)">
						<text class="txt">账号：XXXXXXXXX</text>
						<text class="uni-navigate-icon uni-icon">></text>
					</view>
				</view>
			</view>
		</view>
		 
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item linehei live-line" @click="goPage('333')">
				<text class="uni-navigate-text">修改账号</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage('333')">
				<text class="uni-navigate-text">关于</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		<view class="uni-panel-all mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item " @click="goPage('333')">
				<text class="uni-navigate-text">注册工作室</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view> 
		</view>
		<view class="uni-panel-all mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item " @click="goPage('333')">
				<text class="uni-navigate-text">我的收益</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view> 
		</view>
		
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item linehei live-line" @click="goPage('333')">
				<text class="uni-navigate-text">返回机构</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage('333')">
				<text class="uni-navigate-text">注销账号</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>

<style lang="scss">
	.linehei{
		line-height: 60rpx;
	}
	.mar30 { 
		margin-top: 30rpx;
	}
	.user{
		width: 75%;
		.txt{
			font-size: 0.9em;
		} 
		.uni-icon{
			float: right;
			position: relative;
			top:16rpx;
		}
	}
	.img{ 
		width:120rpx; 
		height: 120rpx;
		overflow: hidden;
		background-color: red;
		border-radius: 60rpx;
		.image{
			width:120rpx;
			height:120rpx;
		}
	}
	
</style>
