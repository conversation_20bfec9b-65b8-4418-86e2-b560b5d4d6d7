<template>
	<!-- 关于 -->
	<view class="uni-container">
		<view class="uni-panel-all" v-for="(item, index) in list" :key="item.id">
			<view @click="goPage(item.url)" class="uni-navigate-item live-flex-panel live-flex-item">
				<text class="uni-navigate-text">{{ item.name }}</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [
					{
						id:1,
						name: '关于直播助手',
						url: ''
					},
					{
						id:2,
						name: '功能说明',
						url: ''
					},
					{
						id:3,
						name: '用户协议',
						url: ''
					},
					{
						id:4,
						name: '隐私政策',
						url: ''
					},
					{
						id:5,
						name: '价格政策',
						url: ''
					},
					{
						id:6,
						name: 'APP下载',
						url: ''
					},
				],
			}
		},
		methods: {
			goPage(url) {
				console.log(url);
				if(url){
					uni.navigateTo({
						url: url
					});
				}else{
					uni.showToast({
						title:"功能未开放，敬请期待！",
							icon: 'none',
							duration: 1000
					})

				}
				
			},
		}
	}
</script>

<style lang="scss">
.mar30{ margin-top: 30rpx; }
.uni-panel-all{
	margin-top: 10px;
}
</style>
