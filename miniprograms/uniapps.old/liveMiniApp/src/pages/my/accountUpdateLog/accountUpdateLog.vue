<template>
	<view class="uni-container">
		<view class="uni-panel-all" >
			<text>当前数据 XXXXXXX</text>
			<text>该数据为第n次修改后的结果</text>
		</view>
		<view class="uni-panel-all">
			<view class="live-flex-panel">
				<view>
					原始信息
				</view>
				<view class="">
					账号
					<view class="">
						创建人：XXX XXXX-XX-XX XX：XX：XX
					</view>
				</view>
			</view>
			<view class="live-flex-panel">
				<view>
					原始信息
				</view>
				<view class="">
					账号
					<view class="">
						创建人：XXX XXXX-XX-XX XX：XX：XX
					</view>
				</view>
			</view>
			<view class="live-flex-panel">
				<view>
					原始信息
				</view>
				<view class="">
					账号
					<view class="">
						创建人：XXX XXXX-XX-XX XX：XX：XX
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss">
.uni-panel-all>view:nth-child(odd){
	background:#fff;
}
</style>
