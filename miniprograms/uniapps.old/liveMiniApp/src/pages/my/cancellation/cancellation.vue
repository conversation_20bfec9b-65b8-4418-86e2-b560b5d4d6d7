<template>
	<!-- 关于 -->
	<view class="uni-container">
		<view class="uni-panel-all">
			<view class="uni-navigate-item">
				<view class="uni-navigate-text">您XXXX年XX月XX日 XX:XX:XX提交了注销账号的申请</view>
				<view class="uni-navigate-text">系统受理时间为XXXX年XX月XX日 XX:XX:XX</view>
			</view>
		</view>

        <view class="uni-panel-all">
			<view class="uni-navigate-item">
				<view class="uni-navigate-text">最近一次缴费金额与缴费时间</view>
				<view class="uni-navigate-text">XX.XX元，XXXX年XX月XX日 XX:XX:XX</view>
			</view>
		</view>

        <view class="uni-panel-all">
			<view class="uni-navigate-item">
				<view class="uni-navigate-text">该次缴费应服务的时间区间</view>
				<view class="uni-navigate-text">XXXX年XX月XX日——XXXX年XX月XX日</view>
			</view>
		</view>

        <view class="uni-panel-all">
			<view class="uni-navigate-item ">
				<view class="uni-navigate-text">该次缴费实际服务的时间区间</view>
				<view class="uni-navigate-text">XXXX年XX月XX日——XXXX年XX月XX日</view>
			</view>
		</view>


        <view class="uni-panel-all">
			<view class="uni-navigate-item ">
				<view class="uni-navigate-text">该次缴费实际服务时间区间发生的费用</view>
				<view class="uni-navigate-text">XX.XX元（本系统使用费用为每日9元）</view>
			</view>
		</view>


        <view class="uni-panel-all">
			<view class="uni-navigate-item ">
				<view class="uni-navigate-text">应退还金额</view>
				<view class="uni-navigate-text">XX.XX元</view>
			</view>
		</view>

        <view class="uni-panel-all">
			<view class="uni-navigate-item tip">
				<view class="uni-navigate-text">！您需在XXXX年XX月XX日XX:XX:XX前确定是否注销。</view>
				<view class="uni-navigate-text">如“暂不注销，返回”，或过期不操作，则本次注销操作失效，且当日不可再次注销。本账号将继续计费。</view>
                <view>如“确定注销”，您将无法再访问系统，以及将无法再见到您的数据。</view>
			</view>
            <view class="uni-navigate-item live-flex-panel live-flex-item mar30">
			    <button class="cancel" type="default" @click="cancellationNo()">暂不注销，返回</button>	
                <button class=" primary" @click="cancellationOk()">确定注销</button>
			</view>
		</view>
	</view>
</template>

<script>
    import { cancellationTeam, organizationList } from "../../../api/api.js" 
	export default {
		data() { 
			return {
                list:[]
			}
		},
		methods: {
            cancellationOk() {
				cancellationTeam().then((res) => {
					console.log('cancellationOk', res) 
                    let sta = res.data.data
					uni.hideLoading()
                    if(sta){
						setTimeout(() => {
							uni.showToast({
								title: '您已成功注销该工作室！应退还金额将原路返回，请注意查收！',
								duration: 3000,
								icon:'none',
								success:function(){
									setTimeout(() => {
										organizationList().then((res) => {
											console.log('organizationList', res)
											let list = (res.data.organizationList) || [] 
											console.log('机构列表', list)
											uni.setStorageSync('officeList', list); 
											uni.redirectTo({ 
												url: '/pages/redirectpage/redirectpage?path=/pages/my/privateSpace/mine/mine',
											}); 
										})
									}, 3000);
									
								
								}
							});
							
						}, 50);
                        
                        
                    }else{
                        uni.showToast({
                            title: '操作失败！',
                            duration: 2000,
                            icon:none
                        });
                    }
                    

				}).catch((err) => {
					console.log('cancellationOk err', res) 

				})
				
			},
            cancellationNo(url) {
				console.log(url);
				uni.navigateBack()
			},
		}
	}
</script>

<style lang="scss"> 
.mar30{ margin-top: 20px; }
.uni-panel-all{
	margin-top: 10px;
    font-size: 14px;
    line-height: 20px;
    color:#333;
}
.tip{
    font-size: 0.8em;
}
</style>
