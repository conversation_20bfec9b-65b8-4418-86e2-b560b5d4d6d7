<template>
	<view class="uni-container">
		<view class="uni-panel-all" >
			<view @click="goPage(logurl)" class="uni-navigate-item live-flex-panel live-flex-panel live-flex-item">
				<text class="uni-navigate-text">账号</text>
				<text>{{ user.mobile }}</text>
			</view>
		</view>
		<view class="uni-panel-all mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-panel live-flex-item">
				<text class="uni-navigate-text">可登录的工作室</text>
				<text>{{liveNum}}个</text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-panel live-flex-item">
				<text class="uni-navigate-text">创建</text>
				<text>{{ (new Date(user.createTime)).format("yyyy年M月d日") }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				logurl:"/pages/accountUpdateLog/accountUpdateLog",
				lacOfficeUrl:"/pages/accessOffice/accessOffice",
				user:uni.getStorageSync('privateUser'),
				org:{},
				officeList: uni.getStorageSync('officeList'),
				liveNum: 0,
			}
		},
		onLoad(){ 
			this.officeList.forEach(offItem => {
				if(offItem.code == 'liveHelper'){
					this.liveNum++;
				}
			})

		},
		methods: {
			goPage(url) {
				console.log(url);
				uni.navigateTo({
					url: url
				});
			},
		}
	}
</script>

<style lang="scss">
.mar30{ margin-top: 30rpx; }
</style>
