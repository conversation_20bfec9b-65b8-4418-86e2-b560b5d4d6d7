<template>
	<view class="uni-container">
		<view class="uni-panel-all" >
			<view class="uni-navigate-item live-flex-panel live-flex-panel live-flex-item">
				<text class="uni-navigate-text">账号</text>
				<text>{{ user.mobile }}</text>
			</view>
		</view>
		<view class="uni-panel-all mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-panel live-flex-item">
				<text class="uni-navigate-text">所在工作室</text>
				<text>{{ org.fullName }}</text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-panel live-flex-item">
				<text class="uni-navigate-text">进入该工作室的时间</text>
				<text>{{ (new Date(user.createTime)).format("yyyy年M月d日 hh:mm:ss") }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				user:{},
				org:{},
			}
		},
		onLoad(){
			// 机构用户信息 和 机构信息
			const user1 = uni.getStorageSync('user')
			const org1 = uni.getStorageSync('org')
			this.user = JSON.parse(user1)
			this.org = JSON.parse(org1) 
		},
		methods: {
			
		}
	}
</script>

<style lang="scss">
.mar30{ margin-top: 30rpx; }
</style>
