<template>
	<view>
		<view class="applyTip">
			<img class="img" src="../../static/img/tip.png" alt="Loading"/><br/>
			字节精灵 想要获取您的抖音账号和昵称<br/>
			用以展示在您的基本信息中<br/>
		</view>
		<button class="primary nextStep" @click="goNext">我已知晓，下一步</button>


	</view>
</template>

<script>

	export default {
		methods: {
			goNext(){
				uni.redirectTo({
					// url: '/pages/startPage/startPage'
					url: '/pages/startPage/startPageWebView'
				});
			}
			
		}
	}
</script>
<style scoped>
#app{ 
	background: #fff;
}
body{
	background: #fff;
}
.nextStep{
	width: 80%;
}
.applyTip{
	padding: 0 50px;
    text-align: center;
    margin: 150px auto;
}
.img{
	position: relative;
    width: 60px;
    height: 60px;
	left: -40%;
}
</style>
