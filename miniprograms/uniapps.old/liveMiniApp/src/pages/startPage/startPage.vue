<template>
	<view>
		<!-- this this start page ! -->
		<view class="loading">
			<img class="img" src="../../static/img/2.gif" alt="Loading">
		</view>
		<view class="applyTip">字节精灵 正在请求获取您的抖音账号和昵称<br/>用以展示在您的基本信息中</view>

	</view>
</template>

<script>
	import { callLogin, getTxt, organizationList, sureLogin } from '../../api/api.js'
	import {auth, localStorage} from '@/sys/auth'

	export default {
		data() {
			return {
				optionData: {}
			}
		},
		onLoad(option) {
			uni.clearStorageSync();
			if(option.type??false) {
				this.optionData = this.checkShare(option)
			}
			this.login(this)
		},
		methods: {
			checkShare(shareOption) {
				//增加检查处理分享参数的代码，如页面显示等，然后继续调用登录，登录后根据登录反馈和分享参数按需求处理
				let info = JSON.parse(JSON.stringify(shareOption))
				uni.setStorageSync('share_message', info);
				if(info.type === 1){ // 分享 注册工作室的
					
				}else{ // 分享  加入工作室的 

				}
				return info
			},
			login(){	
				let that = this
				callLogin().then(res => {
					console.log(res)
					if(res === 'fail'){
						uni.redirectTo({
							url: '/pages/register/register-6/register-6'
						});
						return false
					}
					let data = res.data 
					uni.setStorageSync('loginUser', data.data);
					console.log('登陆返回值 =', data)

					if(data.success>0) {
						 that.getOfficeList()
						
					} else {
						let error = data.error
						// console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
						switch (error.code) {
							case '2': //需要短信验证激活
								break
							case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号
								//	测试
								// that.getTxtFun()
								// 跳转注册页 
								console.log('need register!!')
								that.redirectPage(1)

							    break
							case '4': //可能是授权或者code问题，建议重试tt.login
								// that.redirectPage()
								that.ttLogin()
								break
							case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
								console.log('服务器问题，比如wonderss服务器访问微信/字节服务器异常')
								break
						}
					}
				}).catch(res => {
					console.log('登陆失败=', res)
					that.redirectPage(1)
				})
			},
			getTxtFun() {
				getTxt().then(res => {
					console.log('txt response', res, res.data.data)
				}).catch(res =>{
					console.log('txt error', res)
				});
			},
			ttLogin() {
				tt.login({
					force: true,
					success(res) {
						console.log(`login 调用成功${res.code} ${res.anonymousCode}`);
					},
					fail(res) {
						console.log(`login 调用失败`);
					},
				});
			},
			redirectPage(type){
				let url = '/pages/register/register-1/register-1?need=4'
				if(this.optionData && this.optionData.type && this.optionData.type == 2){// 小分享
					url = '/pages/register/register-4/register-4'
				}
				else if(type == 2){ // 登陆成功 跳转用户 工作室列表
					url = '/pages/officeList/officeList'
				}else{
					if(this.optionData && this.optionData.type && this.optionData.type == 2){// 小分享
						url = '/pages/register/register-4/register-4'
					}else if(this.optionData && this.optionData.type && this.optionData.type == 1){ // 大分享
						url = '/pages/register/register-1/register-1'
					} else{
						
					} 
				} 
				uni.redirectTo({
					url: url
				});
				
			},
			getOfficeList:function(){
				let that = this
				organizationList().then((res) => {
					let oid = this.optionData.oid
					let type = this.optionData.type
					let userMob = auth.getAcc()
					console.log('userMob=', userMob)
					let officeList = (res.data.organizationList) || [] 
					if(officeList.length > 0){
						uni.setStorageSync('officeList', officeList); 
						
						if(this.optionData && type && userMob.mobile){
							let isJoin = false // 是否加入了邀请的机构
							let officeItem = { }
							let liveNum = 0 // 所在工作室的 地方
							let superNumber = 0; // 创建 直播工作室的 数量
							officeList.forEach(offItem => {
								if(Number(offItem.id) === Number(oid)){
									isJoin = true
									officeItem = offItem
								}
								if(offItem.code == 'liveHelper'){
									liveNum++;
								}
								if(offItem.code == 'liveHelper' && offItem.phone == userMob.mobile ){
									superNumber++;
								}
							})
							if(type ==1){ // 大分享
								if(superNumber > 0){ // 已经注册过机构了
									uni.redirectTo({
										url: '/pages/officeList/officeList',
									}); 
								}else{
									uni.redirectTo({
										url: '/pages/register/register-1/register-1',
									}); 
								}

							}else if(type == 2){ // 小分享
								if(isJoin){ // 加入了， 直接进入
									this.goOffice(officeItem)

								}else{ // 没加入的 进邀请页
									uni.redirectTo({
										url: '/pages/register/register-4/register-4',
									}); 
								} 

							}

						}else{
							let isHav = []; // 是直播助手的机构
							officeList.forEach(offItem => {
								if(offItem.code == 'liveHelper'){
									isHav.push(offItem)
								}
							})
							if(isHav.length > 0){ // 有直播助手机构的
								that.redirectPage(2)
							}else{ // 没有直播助手机构的 跳私人领域
								uni.redirectTo({
									url: '/pages/redirectpage/redirectpage?path=/pages/my/privateSpace/mine/mine',
								}); 
							}
						}
					}else{
						// 注销了所有机构
						console.log('没有机构了==')
						if(this.optionData &&  this.optionData.shareName){
							if(type ==1){ // 大分享
								uni.redirectTo({
									url: '/pages/register/register-1/register-1',
								}); 

							}else if(type == 2){ // 小分享
								uni.redirectTo({
									url: '/pages/register/register-4/register-4',
								}); 

							}

						}else{
							uni.redirectTo({
								url: '/pages/redirectpage/redirectpage?path=/pages/my/privateSpace/mine/mine',
							}); 

						}
						

					}
				})
			},
			goOffice:function(officeItem){
				uni.setStorageSync('officeInfo', officeItem);
				let userPhone = uni.getStorageSync('userPhone');
				// 获取用户信息
				sureLogin({ "mobile": userPhone, "oid": officeItem.id }).then((res)=>{
					let userInfo = uni.getStorageSync('userInfo');
					let userLoginDto = res.data.userLoginDto
					let userAuth = res.data.user
					userInfo.orgU = userLoginDto
					userInfo.orgUserAuth = userAuth
					uni.setStorageSync('userInfo', userInfo);

					let user1 = uni.getStorageSync('user')
					const org1 = uni.getStorageSync('org')
					user1 = JSON.parse(user1)

					if(user1.roleCode == 'staff'){
						uni.redirectTo({ 
							url: '/pages/redirectpage/redirectpage?path=/pages/home/<USER>/manage',
						}); 

					}else{
						uni.redirectTo({ 
							url: '/pages/redirectpage/redirectpage?path=/pages/home/<USER>/data',
						}); 

					}

					
				})
			}
			
		}
	}
</script>
<style scoped>
#app{ 
	background: #fff;
}
body{
	background: #fff;
}
.loading{
	width: 200px;
    background: #fff;
    margin: 100px auto;
    height: 200px;
    overflow: hidden;
    border-radius: 100px;
    box-shadow: 0 0 38px #ccc;
}
.applyTip{
	padding:0 50px;
	text-align: center;
	position: relative;
	top:-70px;
}
.img{
	position: relative;
    left: -54px;
}
</style>
