
<template>
	<view>
		<web-view :webview-styles="webviewStyles" :src="src"></web-view>
	</view>
</template>

<script>
import {auth} from '@/sys/auth'
export default {
	data() {
		return {
			webviewStyles: {
				progress: {
					color: '#FF3333'
				}
			},
			src:""
		}
	},
	onLoad: function (option) { //option为object类型，会序列化上个页面传递的参数
		console.log('option' , option); //打印出上个页面传递的参数。
    const baseWebUrl = process.env.VUE_APP_WEBVIEW_ROOT
    auth.getGuestToken().then( token => {
      this.src = baseWebUrl + `?token=${token}&page=${ option.link }`
      // this.src = baseWebUrl
      console.log('跳转的webview', this.src)
    })
	}
}
 
</script>

