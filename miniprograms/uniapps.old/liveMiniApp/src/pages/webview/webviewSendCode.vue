
<template>
	<view>
<!--		<web-view :webview-styles="webviewStyles" :src="src" bindmessage="onMessage"></web-view>-->
		<web-view :webview-styles="webviewStyles" :src="src" ></web-view>
	</view>
</template>

<script>
import {auth} from '@/sys/auth'
export default {
	data() {
		return {
			webviewStyles: {
				progress: {
					color: '#FF3333'
				}
			},
			src:""
		}
	},
	onLoad: function (option) { //option为object类型，会序列化上个页面传递的参数
		console.log('option' , option); //打印出上个页面传递的参数。
    const baseWebUrl = process.env.VUE_APP_WEBVIEW_ROOT
    this.src = baseWebUrl + `?code=${ option.code }`
    // this.src = baseWebUrl
    console.log('跳转的webview', this.src)
	},
  // 接收从h5 发来的消息
  onMessage(options) {
    /**
     * 网页的消息发出顺序为 postMessage1、postMessage2、postMessage3
     * options.detail.data 的详细内容分别为:
     * [{ mes: "postMessage1" }, { mes: "postMessage2" }, { mes: "postMessage3" }]
     */
    console.log("onmessage：", options.detail.data);
  },
  onShareAppMessage(options) {
    console.log(options.webViewUrl); // 当前 web-view 的URL
  },

}
 
</script>

