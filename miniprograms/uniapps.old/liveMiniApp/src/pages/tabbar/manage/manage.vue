<template>
	<view class="uni-container">
		<pageHead title="XXXXXXX的工作室" :showBack="false">
			<template v-slot:rightBtns>
				<view @click="goPage(shareUrl)">share</view>
			</template>
		</pageHead>
		<view class="uni-panel-all uni-font-lit">
			今天是XXXX年X月XX日 星期X
		</view>
		<view class="uni-panel-all live-flex-panel mar30" >
			<view v-if="userInfo" class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage(memberUrl)">
				<text class="uni-navigate-text">团队成员管理</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			memberUrl: '/pages/memberList/memberList',
			shareUrl: '/pages/share/share',
			userInfo: "",
		};
	},
	onLoad() {
		this.userInfo = uni.getStorageSync('userInfo');
	},
	methods: {
		goPage:function(url){
			uni.navigateTo({
				url:url
			})
		}
	}
};
</script>

<style>
 .mar30{ margin-top: 30rpx; }
</style>
