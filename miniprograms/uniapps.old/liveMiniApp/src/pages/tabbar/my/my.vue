<template>
	<view class="uni-container">
		<view class="uni-panel-all live-flex-panel live-flex-item">
			<view class="img">
				<image class="image" src="../../../static/img/tabbar/guanzhuactive.png" mode="头像"></image>
			</view>
			<view class="user">
				<view class="officeName">{{ userInfo.name }}的工作室</view>
				<view class="userInfo">
					<view class="txt">抖音名：XXXXXXXXX</view>
					<view @click="goPage(accountDetails)">
						<text class="txt">手机号：XXXXXXXXX</text>
						<text class="uni-navigate-icon uni-icon">></text>
					</view>
				</view>
			</view>
		</view>
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage(item.url)"
				v-for="(item, index) in menuList" :key="item.id"
			>
				<text class="uni-navigate-text">{{ item.name }}</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item linehei live-line" @click="goPage('333')">
				<text class="uni-navigate-text">切换工作室</text>
				<text class="uni-navigate-icon uni-icon">XX个工作室 ></text>
			</view>
			<view class="uni-navigate-item live-flex-panel live-flex-item">
				<text class="uni-navigate-text">注销工作室</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails",
				menuList:[
					{
						id:1,
						name: '私人领地',
						url: '/pages/privateSpace/privateSpace'
					},
					{
						id:2,
						name: '软件使用费',
						url: ''
					},
					{
						id:3,
						name: '关于',
						url: '/pages/my/about/about'
					},
				],
				userInfo:{},
			}
		},
		onLoad() {
			this.userInfo = uni.getStorageSync('userInfo');
		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>

<style lang="scss">
	.linehei{
		line-height: 60rpx;
	}
	.mar30 { 
		margin-top: 30rpx;
	}
	.user{
		width: 75%;
		.txt{
			font-size: 0.9em;
		} 
		.uni-icon{
			float: right;
			position: relative;
			top:16rpx;
		}
	}
	.img{ 
		width:120rpx; 
		height: 120rpx;
		overflow: hidden;
		background-color: red;
		border-radius: 60rpx;
		.image{
			width:120rpx;
			height:120rpx;
		}
	}
	
</style>
