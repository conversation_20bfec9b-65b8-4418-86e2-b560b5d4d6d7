<template>
	<view class="uni-container">
		<view class="uni-panel-all live-flex-panel live-flex-item">
			<view class="img">
				<image class="image" :src="userImg" mode="头像"></image>
			</view>
			<view class="user">
				<view class="officeName">{{ org.fullName }}</view>
				<view class="userInfo">
					<view class="txt">抖音名：{{ user.userName }}</view>
					<view @click="goPage(accountDetails)">
						<text class="txt">手机号：{{ user.mobile }}</text>
						<text class="uni-navigate-icon uni-icon">></text>
					</view>
				</view>
			</view>
		</view>
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage('/pages/my/privateSpace/life/life',1)">
				<text class="uni-navigate-text"><text class="icon icon-privateArea"></text>私人领地</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view> 
		</view>

		<view class="uni-panel-all live-flex-panel mar30" > 
			<view class="uni-navigate-item live-flex-panel live-flex-item " @click="goPage('', 2)">
				<text class="uni-navigate-text"><text class="icon icon-appFee"></text>软件使用费</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view> 
		</view>

		<view class="uni-panel-all live-flex-panel mar30" > 
			<view class="uni-navigate-item live-flex-panel live-flex-item " @click="goPage('/pages/my/about/about', 3)">
				<text class="uni-navigate-text"><text class="icon icon-about"></text>关于</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		
		<view class="uni-panel-all live-flex-panel mar30" >
			<view class="uni-navigate-item live-flex-panel live-flex-item linehei live-line" @click="goPage('/pages/officeList/officeList')">
				<text class="uni-navigate-text"><text class="icon icon-changeOffice"></text>切换工作室</text>
				<text class="uni-navigate-icon uni-icon">{{ officeList.length }}个工作室 ></text>
			</view> 

			<view v-if="user.roleCode === 'super'" class="uni-navigate-item live-flex-panel live-flex-item linehei2 " @click="cancellation()">
				<text class="uni-navigate-text"><text class="icon icon-cancellation"></text>注销工作室</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>

			<view v-if="user.roleCode !== 'super'" class="uni-navigate-item live-flex-panel live-flex-item" @click="loginOffTeam()">
				<text class="uni-navigate-text"><text class="icon icon-loginOffTeam"></text>退出团队</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		<tabBar :activeIndex="2" :isShowData="isShowData" :mainTab="1"></tabBar>	
	</view>
</template>

<script>
	import {auth, localStorage} from '@/sys/auth'
	import { getRootPathFile, activeMemberOutTeam } from "@/api/api.js";

	import userImgErr from "../../../static/img/tabbar/1.png";
	export default {
		data() {
			return {
				accountDetails: "/pages/my/accountDetails/accountDetails",
				menuList:[
				],
				userInfo:{},
				officeList:[],
				user:[],
				org:[],
				userImg : '',
				isShowData:false
			}
		},
		onLoad() {
			this.getFileRoot()
			
		},
		methods: {
			getFileRoot:function(){
				getRootPathFile().then((res) =>{
					this.userInfo = uni.getStorageSync('userInfo');
					this.officeList = uni.getStorageSync('officeList') || [];
					console.log('getRootPathFile =' , res.data)
					let fileUrl = res.data.fileUrl
					let accInfo = auth.getAcc()
					this.userInfo.avatarUrl = this.userInfo.avatarUrl || (fileUrl + accInfo.avatar)
					this.userImg = this.userInfo.avatarUrl || userImgErr  
					console.log('userInfo', this.userInfo)
					
					// 机构用户信息 和 机构信息
					const user1 = uni.getStorageSync('user')
					const org1 = uni.getStorageSync('org')
					this.user = JSON.parse(user1)
					this.org = JSON.parse(org1)
					if(this.user.roleCode == 'staff'){
						this.isShowData = false
					}else if(this.user.roleCode == 'super'){
						this.isShowData = true
					}
					console.log("机构用户信息 和 机构信息", this.user, this.org) 


				})
			},
			goPage:function(url, itemId){
			 
				if(itemId === 1){
					// let link = 'https://dvm01.btransmission.com/vue/liveAssistant/dist/index.html';
					// console.log('link', link ,  encodeURIComponent(link))
					// uni.navigateTo({
					// 	// url: '../detail/index?link=' + encodeURIComponent(options.link),
					// 	url: "/pages/webview/webview?link=" + (link),
					// });
					uni.redirectTo({
						// url: '/pages/home/<USER>/data' ,
						url: '/pages/redirectpage/redirectpage?path=/pages/my/privateSpace/mine/mine',
						// url: "/pages/webview/webview?link=" + encodeURIComponent(link),
					}); 

				}else{
					if(url){
						uni.navigateTo({
							url:url
						})
					}else{
						uni.showToast({
							title:"功能未开放，敬请期待！",
								icon: 'none',
								duration: 1000
						})
					}
					
				}
				
			},
			loginOffTeam:function(){
				let that = this
				uni.showModal({
					title: '',
					content: '退出团队后您将无法登录该工作室，除非再获邀请。确定退出团队吗？',
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定');
							activeMemberOutTeam().then((res)=>{
								console.log('退出团队', res)
								if(res){
									uni.showToast({
										title:"操作成功",
										icon: 'none',
										duration: 1000,
										success: function () {
											// 还有其他工作室的就 跳 工作室列表， 没有 别的工作室的 跳注册页
											let len = that.officeList.length
											if(len === 1){
												uni.redirectTo({ 
													url: '/pages/register/register-1/register-1'
												}); 
											}else{
												uni.redirectTo({ 
													url: '/pages/officeList/officeList'
												}); 
											}
											
										}
									})
								}else{
									uni.showToast({
										title:"操作失败",
										icon: 'none',
										duration: 1000
									})
								}

							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			cancellation:function(){
				uni.navigateTo({
					url: '/pages/my/cancellation/cancellation'
				})
			}

		}
	}
</script>

<style lang="scss">
	.img{ 
		width:120rpx; 
		height: 120rpx;
		display: inline-block;
		overflow: hidden;
		background-color: #eee;
		background-size: 100%;
		border-radius: 60rpx;
		// background-image: url("");
		.image{
			width:120rpx;
			height:120rpx;
		}
	}
	.icon-privateArea{ background: url("../../../static/img/privateArea.png"); }
	.icon-appFee{ background: url("../../../static/img/appFee.png"); }
	.icon-about{ background: url("../../../static/img/about.png"); }
	.icon-changeOffice{ background: url("../../../static/img/changeOffice.png"); }
	.icon-loginOffTeam{ background: url("../../../static/img/loginOffTeam.png"); }
	.icon-cancellation{ background: url("../../../static/img/cancellation.png"); }
	.icon{
		width: 32rpx;
		height: 32rpx;
		background-color: #fff;
		background-size: 100%;
		display: inline-block;
		position: relative;
		top:6rpx;
		margin-right:14rpx;
	}
	.linehei{
		line-height: 60rpx;
	}
	.linehei2{
		line-height: 74rpx;
	}
	.mar30 { 
		margin-top: 30rpx;
	}
	.user{
		width: 75%;
		.txt{
			font-size: 0.9em;
		} 
		.uni-icon{
			float: right;
			position: relative;
			top:16rpx;
		}
	}


	
</style>
