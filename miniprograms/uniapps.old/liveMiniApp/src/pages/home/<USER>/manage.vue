<template>
	<view class="uni-container">
		<pageHead :title="org.fullName" :showBack="false">
			<template v-slot:rightBtns>
				<view  @click="goPage(shareUrl)" class="share"></view>
			</template>
		</pageHead>
		<view class="uni-panel-all uni-font-lit">
			今天是 {{ curDate }} 星期{{ weekDay }}
		</view>
		<view class="uni-panel-all live-flex-panel mar30" v-if="user.roleCode == 'super'" >
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage(memberUrl)">
				<view class="uni-navigate-text"><icon class="icon timer"></icon>团队成员管理</view>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>

    <view class="uni-panel-all live-flex-panel mar30" >
      <view class="uni-navigate-item live-flex-panel live-flex-item" @click="goWebView('musicLyric')">
        <view class="uni-navigate-text"><icon class="icon gequ"></icon>歌曲管理</view>
        <text class="uni-navigate-icon uni-icon">></text>
      </view>
      <view class="uni-navigate-item live-flex-panel live-flex-item" @click="goWebView('xiqu')">
        <view class="uni-navigate-text"><icon class="icon xiqu"></icon>戏曲管理</view>
        <text class="uni-navigate-icon uni-icon">></text>
      </view>
      <view class="uni-navigate-item live-flex-panel live-flex-item" @click="goWebView('zimuSet')">
        <view class="uni-navigate-text"><icon class="icon zimu"></icon>字幕设置</view>
        <text class="uni-navigate-icon uni-icon">></text>
      </view>
    </view>

		<tabBar :activeIndex="1" :isShowData="isShowData" :mainTab="1"></tabBar>	
	</view>
</template>

<script>
	import { ttAassistantSharing } from '../../../api/api.js'

export default {
	data() {
		return {
			memberUrl: '/pages/manage/memberList/memberList',
			shareCbUrl: '/pages/startPage/startPage',
			shareUrl: '/pages/manage/share/share',
			user: "",
			org: "",
			curDate: "",
			weekDay: "", 
			shareTtAppMes:"",
			isShowData:false

		};
	},
	onLoad() {
		this.weekDay = "日一二三四五六".charAt(new Date().getDay()); 
		this.curDate = (new Date()).format("yyyy年M月d日")
		// 机构用户信息 和 机构信息
		const user1 = uni.getStorageSync('user')
		const org1 = uni.getStorageSync('org')
		this.user = JSON.parse(user1)
		this.org = JSON.parse(org1)
		if(this.user.roleCode == 'staff'){
			this.isShowData = false
		}else if(this.user.roleCode == 'super'){
			this.isShowData = true
		}
		console.log("机构用户信息 和 机构信息", this.user, this.org) 
		this.getShareData()

	},
	methods: {
    goWebView(type){
      let ftpURl = 'home'
      uni.navigateTo({
        url:`/pages/webview/webview?link=${type}`
      })
    },
		getShareData(){
				let user = uni.getStorageSync('user')
				user = JSON.parse(user)
				let sendDate = {
					subOrg:user.oid,
					accId:user.accId,
					type:1,
					module:"assistant",
				}
				ttAassistantSharing(sendDate).then(res => {
				  console.log('gai')
					console.log('ttAassistantSharing response', res.data.data)
					this.shareTtAppMes = res.data.data.shareTtAppMes
					let that = this
					// let parh =  `/pages/manage/share/share?shareTime=${ that.shareTtAppMes.shareTime }&shareName=${ that.shareTtAppMes.shareName }&type=1`
					let query =  `shareTime=${ that.shareTtAppMes.shareTime }&shareName=${ that.shareTtAppMes.shareName }&type=1`

					this.shareTtAppMes.path = this.shareCbUrl
					this.shareTtAppMes.query = query
					uni.setStorageSync('shareTtAppMes', this.shareTtAppMes);
					console.log('shareTtAppMes ==  ', this.shareTtAppMes)
				}).catch(res =>{
					console.log('ttAassistantSharing error', res)
				});
			},
		goPage:function(url){
			console.log(url);
			uni.navigateTo({
				url:url
			})
		}
	}
};
</script>

<style scoped>
 .mar30{ margin-top: 30rpx; }
 .share{
    width: 25px;
    height: 25px;
    background-image: url("../../../static/img/share.png");
    background-size: 100%;
    background-repeat: no-repeat;
    margin:8px 10px 0 5px;
 }
 .timer{ background: url('../../../static/img/tuanduichengyuan.png') no-repeat; }
 .gequ{ background: url('../../../static/img/xiqu.png') no-repeat; }
 .xiqu{ background: url('../../../static/img/xiqu.png') no-repeat; }
 .zimu{ background: url('../../../static/img/zimushezhi.png') no-repeat; }

 .icon{
   width: 20px;
   height: 20px;
   display: inline-block;
   background-size: 20px;
   margin-right: 10px;
   position: relative;
   top:5px;
 }
</style>
