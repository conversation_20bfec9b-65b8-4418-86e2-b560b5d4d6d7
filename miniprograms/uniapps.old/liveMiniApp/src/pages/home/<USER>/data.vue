<template>
	<view class="uni-container">
		<pageHead :title="org.fullName" :showBack="false"/>
		<view class="uni-panel-all uni-font-lit">
			今天是 {{ curDate }} 星期{{ weekDay }}
		</view>
		<tabBar :activeIndex="0" :isShowData="isShowData" :mainTab="1"></tabBar>	
	</view>
</template> 

<script>
export default {
	data() {
		return {
			curDate: "",
			weekDay: "",
			user:{},
			org:{},
			isShowData:false
		};
	},
	onLoad() {
		this.curDate = (new Date()).format("yyyy年M月d日")
		this.weekDay = "日一二三四五六".charAt(new Date().getDay()); 
		// 机构用户信息 和 机构信息
		const user1 = uni.getStorageSync('user')
		const org1 = uni.getStorageSync('org')
		this.user = JSON.parse(user1)
		this.org = JSON.parse(org1)
		if(this.user.roleCode == 'staff'){
			this.isShowData = false
		}else if(this.user.roleCode == 'super'){
			this.isShowData = true
		}
		console.log("机构用户信息 和 机构信息", this.user, this.org) 
	},
	methods: {}
};
</script>

<style>
 
</style>
