<template>
	<view class="uni-container">
		<pageHead :title="org.fullName" :showBack="false">
			<template v-slot:rightBtns>
			</template>
		</pageHead>

    <view class="handleC">
      <view @click="tabNum(1)" :class="activeH === 1 ? 'activeH' :''">任务/申请（X）</view>
      <view @click="tabNum(2)" :class="activeH === 2 ? 'activeH' :''">处理/审批（X）</view>
      <view @click="tabNum(3)" :class="activeH === 3 ? 'activeH' :''">消息（X）</view>
    </view>
    <view v-show="activeH === 1">
      <view class="uni-panel-all live-flex-panel mar30" >
        <view class="uni-navigate-item live-flex-panel live-flex-item" @click="goWebView('lyricTask')">
          <view class="uni-navigate-text"><icon class="timer"></icon>歌词任务</view>
          <text class="uni-navigate-icon uni-icon">></text>
        </view>
      </view>
    </view>
    <view v-show="activeH === 2">
      <view class="uni-panel-all live-flex-panel mar30" >
        <view class="uni-navigate-item live-flex-panel live-flex-item" @click="goWebView('lyricTaskHandle')">
          <view class="uni-navigate-text"><icon class="timer"></icon>歌词任务处理</view>
          <text class="uni-navigate-icon uni-icon">></text>
        </view>
        <view class="uni-navigate-item live-flex-panel live-flex-item" @click="goWebView('lyricTaskApprove')">
          <view class="uni-navigate-text"><icon class="timer"></icon>歌词任务审批</view>
          <text class="uni-navigate-icon uni-icon">></text>
        </view>
      </view>
    </view>
    <view v-show="activeH === 3">
      <view class="nullKlass">
        暂无消息数据
      </view>
    </view>
		<tabBar :activeIndex="6" :isShowData="isShowData" :mainTab="1"></tabBar>
	</view>
</template>

<script>
	import { ttAassistantSharing } from '../../../api/api.js'

export default {
	data() {
		return {
			user: "",
			org: "",
      activeH: 1,
			shareTtAppMes:"",
			isShowData:false

		};
	},
	onLoad() {
		// 机构用户信息 和 机构信息
		const user1 = uni.getStorageSync('user')
		const org1 = uni.getStorageSync('org')
		this.user = JSON.parse(user1)
		this.org = JSON.parse(org1)
		if(this.user.roleCode == 'staff'){
			this.isShowData = false
		}else if(this.user.roleCode == 'super'){
			this.isShowData = true
		}
		console.log("机构用户信息 和 机构信息", this.user, this.org) 

	},
	methods: {
    tabNum(num){
      this.activeH = num
    },
    goWebView(type){
      let ftpURl = 'home'
      uni.navigateTo({
        url:`/pages/webview/webview?link=${type}`
      })
    },
		goPage:function(url){
			console.log(url);
			uni.navigateTo({
				url:url
			})
		}
	}
};
</script>

<style lang="scss" scoped>
.mar30{ margin-top: 30rpx; }
 .handleC{
   display: flex;
   padding-top: 10px;
   background-color: #fff;
   >view{
     flex: 1;
     line-height: 30px;
     border-bottom: 2px solid #fff;
     text-align: center;
     &.activeH{
       border-bottom-color: $live-page-head-bg;
     }
   }
 }
 .nullKlass{
   line-height: 200px;
   color: #ccc;
   font-size: 26px;
   text-align: center;
 }

</style>
