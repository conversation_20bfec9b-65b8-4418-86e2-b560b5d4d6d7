<template>
	<!-- 管理-分享 -->
	<view class="uni-container">
		<pageHead title="小程序分享" :showBack="true">
		</pageHead>
		<view class="uni-panel-all ">
			<view>“主播助手”主要功能</view>
			<view class="dotcon">
				<view><text class="dot"></text>发布直播场次预告</view>
				<view><text class="dot"></text>发布某场次的节目单</view>
				<view><text class="dot"></text>发动团队进行调研</view>
				<view><text class="dot"></text>针对调研结果采取营销活动</view>
			</view>
		</view>
		
		<view class="uni-panel-all mar30" v-for="(item, index) in list" :key="item.id">
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage(item.url)">
				<text class="uni-navigate-text">{{item.name}}</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		<view class="uni-panel-all mar30 live-txt-orange">
			将主播助手分享给你想帮助的主播吧！
		</view>
		<view class="uni-panel-nobg mar30">
<!--			<button class=" primary" @click="shareBtn(register2URL)">分享</button>-->
			<button class="primary" plain="true" data-name="shareBtn" open-type="share" >分享</button>
		</view>
<!--		<button class="separate-fl" plain="true" data-name="shareBtn" open-type="share">分享2</button>-->
	</view>
</template>

<script>

	export default {
		data() {
			return {
				list: [ 
					{
						id:3,
						name: '价格政策',
						url: '/pages/register/pricePolicy/pricePolicy'
					},
					{
						id:4,
						name: '提成政策',
						url: '/pages/manage/commissionPolicy/commissionPolicy'
					},
					],
					register2URL:"/pages/register/register-2/register-2",
					shareTtAppMes: uni.getStorageSync('shareTtAppMes'),
			}
		},
		onLoad(query) {
			// this.showShareMenu()
			console.log('query query = ', query)
			if (query.from === "sharebuttonabc") {
			// do something...
			}
			console.log('shareTtAppMes', this.shareTtAppMes);
			console.log('shareTtAppMes1', this.shareTtAppMes.value);
		},
		onShareAppMessage: function( shareOption ){
			// console.log('options??',shareOption)
			// console.log('options ',shareOption.target.dataset)
			// console.log(' 分享内容 ')
			// if (res.from === 'button') {
			// 	// 来自页面内分享按钮
			// 	console.log(res.target)
			// }
			// return {
			// 	title: '自定义分享标题',
			// 	path: '/pages/test/test?id=123'
			// }

			var that = this;
			// 设置菜单中的转发按钮触发转发事件时的转发内容
			var shareObj = {
				title: that.shareTtAppMes.title,    // 默认是小程序的名称(可以写slogan等)
				path: that.shareTtAppMes.path +"?"+ that.shareTtAppMes.query, // 默认是当前页面，必须是以‘/'开头的完整路径
				imageUrl: '../../../static/img/video.png',   //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
				success: function(res){
					console.log(" success res ", res)
					// 转发成功之后的回调
					if(res.errMsg == 'shareAppMessage:ok'){
						console.log('页面', that.shareTtAppMes.path)
						console.log('参数', that.shareTtAppMes.query)
					}
				},
				fail: function(res){
					console.log(" fail res ", res)

					// 转发失败之后的回调
					if(res.errMsg == 'shareAppMessage:fail cancel'){
						// 用户取消转发
					}else if(res.errMsg == 'shareAppMessage:fail'){
						// 转发失败，其中 detail message 为详细失败信息
					}
				}
			};
			console.log('shareObj', shareObj)

			return shareObj;
		},
		methods: {
			showShareMenu(menus) {
				console.log('进入');
				// const currentMenus = menus.currentTarget.dataset.menus.split(',');
				const that = this;
				tt.showShareMenu({
				menus: ["share"],
				success(res) {
					// 当 API 成功执行后调用，预定义返回消息格式为${API_NAME}:ok
					console.log(res.errMsg);
					             
				},

				fail(res) {
					// 当 API 执行失败后调用, 预定义返回消息格式为${API_NAME}:fail
					console.log(res.errMsg);
				},

				complete(res) {
					// 当 API 执行完成（无论成功或者失败）后都会调用, 预定义返回消息格式为${API_NAME}:ok / fail
					console.log(res.errMsg);
				}
				});
			},

			goPage(url) {
				console.log(url);
				uni.navigateTo({
					url: url
				});
			},
			tipBtn: function (){
				uni.showToast({
					title: '您输入的验证码有误！',
					icon: 'none',
					duration: 3000
				});
			},
			shareBtn:function(){
				// onShareAppMessage(){
					console.log('onShareAppMessageonShareAppMessage 222')
				// },
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mar30{ margin-top: 40rpx;}
.dotcon{
	&>view{
		display: flex;
		flex-direction: column;
		flex-shrink: 0;
		flex-grow: 0;
		flex-basis: auto;
		align-items: stretch;
		align-content: flex-start;
		padding-left: 20rpx;
	}
	.dot{
		background: #666;
		width: 8rpx;
		height: 8rpx;
		border-radius: 4rpx;
		margin:auto 20rpx;
		position: relative;
		bottom: -24rpx;
		left: -40rpx;
	}
}
.uni-icon{
	font-size: 28rpx;
}  
.cancel{
	margin-top: 28rpx;
}
</style>
