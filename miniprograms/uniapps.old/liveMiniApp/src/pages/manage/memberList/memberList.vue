<template>
	<!-- 团队成员管理 -->
	<view class="uni-container">
		<pageHead title="团队成员管理" :showBack="false"/>
		<view class="uni-panel-all uni-font-lit" open-type="share">
			<button class="mbStyle" data-name="shareBtn" open-type="share" >分享给自己的好友或粉丝，招纳其成为团队成员</button>
			
			
		</view>
		<view class="uni-panel-all fon14">
			<text>您本人以外的团队成员现共{{ list.length }}名</text>
			<text class="live-link-btn right" @click="goPage(memberOutListUrl)">已移出团队的成员 ></text>
		</view>
		<!-- <view>
			<img :src="video" alt="">
		</view> -->
		<view class="officeList">
			<liveTable :columns="columns" :list="list" @longPress="longPress"></liveTable>  
		</view>
	</view>
</template>

<script>
	import video from '../../../static/img/video.png'
	import { getTeamMembers, ttAassistantSharing, memberOutTeam } from '../../../api/api.js'
	export default {
		data() {
			return {
				memberOutListUrl: "/pages/manage/memberOutList/memberOutList",
				shareCbUrl: '/pages/startPage/startPage',
				shareTtAppMes:{},
				columns: [{
						title: "抖音名",
						key: "name",
						flex:1
					},
					{
						title: '加入时间',
						key: 'time',
						flex:2
					},
					{
						title: '手机号(账号)',
						key: 'mobile',
						flex:1
					}
				],
				// list: [{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},
				// 	],
				list:[],
				video: video
			}
		},
		onLoad(){
			this.getMembers();
			this.getShareData()
		},
		onShareAppMessage: function(){
			var that = this;
			var shareObj = {
				title: that.shareTtAppMes.title,    // 默认是小程序的名称(可以写slogan等)
				path: that.shareTtAppMes.path +"?"+ that.shareTtAppMes.query, // 默认是当前页面，必须是以‘/'开头的完整路径
				imageUrl: '../../../static/img/video.png',   //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
				success: function(res){
					console.log(" success res ",res)
					// 转发成功之后的回调
					if(res.errMsg == 'shareAppMessage:ok'){
						console.log('页面', that.shareTtAppMes.path)
						console.log('参数', that.shareTtAppMes.query)
					}
				},
				fail: function(res){
					console.log(" fail res ", res)

					// 转发失败之后的回调
					if(res.errMsg == 'shareAppMessage:fail cancel'){
						// 用户取消转发
					}else if(res.errMsg == 'shareAppMessage:fail'){
						// 转发失败，其中 detail message 为详细失败信息
					}
				}
			};
			console.log('shareObj', shareObj)

			return shareObj;
		},
		methods: {
			getShareData(){
				let user = uni.getStorageSync('user')
				user = JSON.parse(user)
				let sendDate = {
					subOrg:user.oid,
					accId:user.accId,
					type:2,
					module:"assistant",
				}
				ttAassistantSharing(sendDate).then(res => {
					console.log('ttAassistantSharing response', res.data.data)
					this.shareTtAppMes = res.data.data.shareTtAppMes
					let that = this
					// let parh =  `/pages/manage/share/share?shareTime=${ that.shareTtAppMes.shareTime }&shareName=${ that.shareTtAppMes.shareName }&type=1`
					let query =  `shareTime=${ that.shareTtAppMes.shareTime }&shareName=${ that.shareTtAppMes.shareName }&type=2&oid=${ user.oid }`

					this.shareTtAppMes.path = this.shareCbUrl
					this.shareTtAppMes.query = query
					uni.setStorageSync('shareTtAppMes', this.shareTtAppMes);
					console.log('shareTtAppMes ==  ', this.shareTtAppMes)
				}).catch(res =>{
					console.log('ttAassistantSharing error', res)
				});
			},
			getMembers:function(){
				getTeamMembers().then(res => {
					console.log('getTeamMembers response', res, res.data.data)
					// let data = {"page":null,"success":1,"data":[{"userID":6722,"userName":"李懂事","departName":"","mobile":"18612591136","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":6723,"userName":"李懂事","departName":"","mobile":"18612591136","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":6724,"userName":"李懂事","departName":"","mobile":"18612591136","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":6725,"userName":"员工四号","departName":"","mobile":"18600000004","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7052,"userName":"李懂事代员工六号八百标兵奔北坡","departName":"","mobile":"18612591136","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7340,"userName":"111","departName":"","mobile":"17123456789","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7341,"userName":"222","departName":"","mobile":"15577585321","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7342,"userName":"员工十八","departName":"","mobile":"18600000018","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7492,"userName":"员工二十","departName":"","mobile":"18600000020","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7721,"userName":"测试","departName":"","mobile":"10011111110","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7722,"userName":"测试","departName":"","mobile":"10011111111","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7723,"userName":"测试","departName":"","mobile":"10011111112","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7724,"userName":"测试","departName":"","mobile":"10011111113","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7725,"userName":"测试","departName":"","mobile":"10011111114","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7726,"userName":"测试","departName":"","mobile":"10011111115","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7727,"userName":"测试","departName":"","mobile":"10011111116","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7728,"userName":"测试","departName":"","mobile":"10011111117","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7729,"userName":"测试","departName":"","mobile":"10011111120","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7730,"userName":"测试","departName":"","mobile":"10011111121","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7731,"userName":"测试","departName":"","mobile":"10011111122","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7732,"userName":"测试","departName":"","mobile":"10011111123","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7733,"userName":"测试","departName":"","mobile":"10011111124","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7734,"userName":"测试","departName":"","mobile":"13511111112","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7735,"userName":"测试","departName":"","mobile":"10011111125","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7736,"userName":"测试","departName":"","mobile":"10011111127","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7737,"userName":"测试","departName":"","mobile":"10011111128","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7738,"userName":"测试","departName":"","mobile":"10011111129","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7739,"userName":"测试","departName":"","mobile":"10011111130","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7740,"userName":"测试","departName":"","mobile":"10011111131","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7741,"userName":"测试","departName":"","mobile":"10011111132","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7742,"userName":"测试","departName":"","mobile":"10011111133","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7743,"userName":"测试","departName":"","mobile":"10011111134","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7744,"userName":"测试","departName":"","mobile":"10011111135","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7745,"userName":"测试","departName":"","mobile":"10011111136","isDuty":"1","gender":null,"imgPath":null,"roleCode":null},{"userID":7746,"userName":"测试","departName":"","mobile":"10011111137","isDuty":"1","gender":null,"imgPath":null,"roleCode":null}],"error":null,"others":null}
					// let members = data.data
					
					let members = res.data.data;

					if(members.length > 0){
						members.forEach(item => {
							if(item.submitState == 0){
								item.mobile = '已设为不展示'
							}
							this.list.push({
								name: item.userName,
								time: "2020-02-02 12:12:12",
								mobile: item.mobile,
								id: item.userID,
								 
							})
						});
						
					}

				}).catch(res =>{
					console.log('txt error', res)
				});
			},
			longPress(item, index){
				console.log('index =', index);
				let that = this
				// console.log(item)
				uni.showActionSheet({
					itemList: ['移出团队', '取 消'],
					success: function (res) {
						console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
						if(res.tapIndex === 0){

							// 移除团队
							console.log('移除的项：', item)
							memberOutTeam({ outUserId: item.id }).then((res)=>{
								console.log('memberOutTeam res:', res)
								if(res.data){
									uni.showToast({
										title: '操作成功',
										duration: 1000,
										icon:'none'
									});
									that.list.splice(index,1);

								}else{
									uni.showToast({
										title: '操作失败',
										duration: 1000,
										icon:'none'
									});

								}
							})
						} 
					},
					fail: function (res) {
						console.log(res.errMsg);
					}
				});
				
			},
			goPage (url){
				console.log(url)
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.mbStyle{
	background: #fff; 
	font-size: 14px;
}
.officeList{
	padding-bottom: 200px;
	&>view{
		padding:20rpx 32rpx;
	}
	&>view:nth-child(odd){
		background-color: #fff;
	}
}
.fon14{
	font-size: 28rpx;
	margin-top:30rpx;
}
.right{
	float: right; 
}
</style>
