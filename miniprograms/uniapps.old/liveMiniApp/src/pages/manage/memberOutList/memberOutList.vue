<template>
	<!-- 已移出团队的成员 -->
	<view class="uni-container">
		<pageHead title="团队成员管理" :showBack="true"/>
		<view class="uni-panel-all fon14">
			<text>已移出您团队的成员现共{{ list.length }}名</text>
		</view>
		<view class="officeList">
			<liveTable :columns="columns" :list="list" @longPress="longPress"></liveTable> 
		</view>
	</view>
</template>

<script>
	import { getOutTeamMembers, memberBackTeam } from '../../../api/api.js'

	export default {
		data() {
			return {
				columns: [{ 
						title: "抖音名",
						key: "name",
						flex:1
					},
					{
						title: '加入时间',
						key: 'time',
						flex:2
					},
					{
						title: '手机号(账号)',
						key: 'mobile',
						flex:1
					}
				],
				list:[],
				// list: [{
				// 			name: '<PERSON>',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},{
				// 			name: '<PERSON>',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},{
				// 			name: '<PERSON>',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},{
				// 			name: '<PERSON> <PERSON>',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",
							
				// 		},
				// 	],
			}
		},
		onLoad(){
			this.getOutMembers(); 
		},
		methods: {
			longPress:function(item){
				console.log('backUserId=' , item)
				uni.showActionSheet({
					itemList: ['重新移入团队', '取 消'],
					success: function (res) {
						if(res.tapIndex === 0){
							// 重新移入团队
							memberBackTeam({ 'backUserId': item.id }).then(res => {
								console.log('memberBackTeam=' , res)
								if(res.data){
									uni.showToast({
										title: '操作成功',
										duration: 1000,
										icon:'none'
									});
									that.list.splice(index,1);

								}else{
									uni.showToast({
										title: '操作失败',
										duration: 1000,
										icon:'none'
									});

								}

							})
							
						}else{}
					},
					fail: function (res) {
						console.log(res.errMsg);
					}
				});
				
			},
			getOutMembers: function(){
				getOutTeamMembers().then(res => {
					console.log('getOutTeamMembers = ', res.data)
					let members = res.data.data;

					if(members.length > 0){
						members.forEach(item => {
							if(item.submitState == 0){
								item.mobile = '已设为不展示'
							}
							this.list.push({
								name: item.userName,
								time: "2020-02-02 12:12:12",
								mobile: item.mobile,
								id: item.userID,
							})
						});
						
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.officeList{
		&>view{
			padding:20rpx 32rpx;
		}
		&>view:nth-child(odd){
			background-color: #fff;
		}
	}
	.fon14{
		font-size: 28rpx;
		margin-top:30rpx;
	}
	.right{
		float: right; 
	}
</style>
