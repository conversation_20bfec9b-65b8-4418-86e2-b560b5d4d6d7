<template>
	<view class="uni-container">
		<view v-if="shareMesage.type==1" class="uni-panel-all live-flex-panel uni-font-lit">
			本条数据由{{ shareMesage.shareName}}于{{shareMesage.shareTime }} 分享
		</view>
		<view class="uni-panel live-flex-panel">
			<view>“字幕精灵”主要功能</view>
			<view class="dotcon">
				<view><text class="dot"></text>发布直播场次预告</view>
				<view><text class="dot"></text>发布某场次的节目单</view>
				<view><text class="dot"></text>发动团队进行调研</view>
				<view><text class="dot"></text>针对调研结果采取营销活动</view>
			</view>
			
		</view>
		<view class="uni-panel live-flex-panel">
			<view>如您已为直播购置话筒等装备、直播事业刚开始或已遭遇瓶颈，请使用“字幕精灵”！</view>
			<view>字幕精灵，将为您的直播事业提供助力！</view>
		</view>
		
		<view class="uni-panel live-flex-panel" v-for="(item, index) in list" :key="item.id">
			<view class="uni-navigate-item live-flex-panel live-flex-item" @click="goPage(item.url)">
				<text class="uni-navigate-text">{{item.name}}</text>
				<text class="uni-navigate-icon uni-icon">></text>
			</view>
		</view>
		<view class="uni-panel-nobg">
			<button class=" primary" @click="goPage('register2URL')" >注册</button>
			<!-- <button class=" primary" @click="goPage('register2URL')" v-if="need !== '4'">注册</button> -->
			<!-- <button class="cancel" type="default" @click="tipBtn">退出</button>		 -->
			<button class="cancel" open-type="launchApp" app-parameter="wechat" binderror="launchAppError">退出</button>
	
		</view>
	</view>
</template>

<script>
import { dd } from '@/utils/DateFormat.js'
import {auth } from '@/sys/auth'

	export default {
		data() {
			return {
				list: [
					{
						id:1,
						name: '用户协议',
						url: '/pages/register/userAgreement/userAgreement'
					},
					{
						id:2,
						name: '隐私政策',
						url: '/pages/register/userAgreement/userAgreement'
					},
					{
						id:3,
						name: '价格政策',
						url: '/pages/register/pricePolicy/pricePolicy'
					},
					],
					// register2URL:"/pages/home/<USER>/data"
					// register2URL:"/pages/officeList/officeList"
					register2URL:"/pages/register/register-2/register-2",
					shareMesage:{},
					need:0
			}
		},
		onLoad(option){
			console.log('option=', option)
			this.need = option.need;
			 
			let userInfo = uni.getStorageSync('userInfo');
			console.log('userInfo = ', userInfo)
			if(userInfo.length > 5){
				this.need = 1;
			}else{
				this.need = '4';
			}
			this.shareMesage = uni.getStorageSync('share_message');
			console.log('this.shareMesage = ', this.shareMesage)
			if(this.shareMesage.type){
				this.shareMesage.shareTime =  new Date(Number(this.shareMesage.shareTime)).format("yyyy-MM-dd hh:mm") 
			}

		},
		methods: {
			goPage(url) {
				console.log(url);
				let user = auth.getAcc()
				if(url === 'register2URL'){
					if(user.mobile){
						url = '/pages/register/register-3/register-3'
					}else{
						url = this[url]
					}
				}
				
				uni.navigateTo({
					url: url
				});
			},
			tipBtn: function (){
				// uni.showToast({
				// 	title: '您输入的验证码有误！',
				// 	icon: 'none',
				// 	duration: 3000
				// });
				
			}
		}
	}
</script>

<style lang="scss" scoped>
.dotcon{
	&>view{
		display: flex;
		flex-direction: column;
		flex-shrink: 0;
		flex-grow: 0;
		flex-basis: auto;
		align-items: stretch;
		align-content: flex-start;
		padding-left: 20rpx;
	}
	.dot{
		background: #666;
		width: 8rpx;
		height: 8rpx;
		border-radius: 4rpx;
		margin:auto 20rpx;
		position: relative;
		bottom: -24rpx;
		left: -40rpx;
	}
}
.uni-icon{
	font-size: 28rpx;
}  
.cancel{
	margin-top: 28rpx;
}
</style>
