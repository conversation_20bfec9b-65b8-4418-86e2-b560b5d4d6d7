<template>
	<view>
		<!-- this this start page ! -->
		<view class="loading">
			<img class="img" src="../../../static/img/logo.png" alt="Loading">
		</view>
		<view class="applyTip">字节精灵 尚未获取您的抖音账号和昵称<br/>暂时无法使用如何</view>

	</view>
</template>

<script>
	// import pageHead form "@/component/page-head/page-head.vue"
	export default {
		data() {
			return {
			}
		},
		methods: {
		}
	}
</script>

<style scoped>
#app{ 
	background: #fff;
}
body{
	background: #fff;
}
.loading{
	width: 200px;
    background: #fff;
    margin: 100px auto;
    height: 200px;
    overflow: hidden;
    border-radius: 100px;
    box-shadow: 0 0 38px #ccc;
}
.applyTip{
	padding:0 50px;
	text-align: center;
	position: relative;
	top:-70px;
}
.img{
	display: block;
	width: 150px;
	margin:30px auto;
	height: 150px;
	position: relative;
	left: 5px;
}
</style>
