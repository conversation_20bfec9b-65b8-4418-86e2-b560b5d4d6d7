<template>
	<view >
		<pageHead title="注册主播助手" :showBack="true">
		</pageHead>
		<!-- <view class="uni-panel-all martop20">
			<view>— 本系统的使用费为每日9元，需预缴费</view>
			<view>— 账号可注销，注销时将按日扣费，余额原路退还</view>
			<view>— 主播助手分为体验版与标准版</view>
			<view>— 当前您只能注册体验版</view>
			<view>— 体验版已能满足您的基本需要</view>
			<view>— 注销时的按日扣费，按每日9元计，与套餐无关 </view>
		</view>
		<view class="uni-panel-all martop20">
			<view>
				<text>请选择一种体验版的套餐：</text>
				<view class="tab1">
					<radio-group @change="radioChange">
						<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in items" :key="item.value">
							<view>
								<radio :value="item.value" :checked="index === current" style="transform:scale(0.7)" color="#0CA751"/>
								{{item.name}}
							</view>
						</label>
					</radio-group>
				</view>
			</view>
		</view> -->
		<view class="zhuceing">正在注册 ...... </view>
		<!-- <view class="uni-panel-all">
			<button class="primary nextStep" :loading="isloading" @click="nextPay">下一步</button>
		</view> -->
	</view>
</template>

<script>
	import { registerSonOrg  } from '../../../api/api.js'

	export default {
		data() {
			return {
				current:"",
				username:"",
				items:[
					{ "value": "1", "name": '一次缴纳5天的使用费，无优惠，缴费45元'},
					{ "value": "2", "name": '一次缴纳21天的使用费，优惠至138元'},
					{ "value": "3", "name": '一次缴纳90天的使用费，优惠至388元'},
					{ "value": "4", "name": '一次缴纳365天的使用费，优惠至888元'},
				],
			}
		},
		onLoad() {
			let that = this;
			let userInfo = uni.getStorageSync('userInfo');
			console.log("userInfo==", userInfo)
			this.username = userInfo.nickName
			this.nextPay()
			// if(uni.getUserProfile) {
			// 	// uni.getUserProfile({
			// 	// 	desc: '获取用户昵称', 
			// 	// 	success: function (loginRes) {
			// 	// 		console.log(loginRes.authResult);
			// 	// 		that.username = ""
			// 	// 	},
			// 	// 	fail: function(err){
			// 	// 		console.log("获取用户信息失败！" , err);
			// 	// 		that.username = "未命名"
			// 	// 		that.getUserInfoByTT();
			// 	// 	}
			// 	// });
			// }else{
				

			// }
		},
		methods: {
			getUserInfoByTT:function(){
				let that = this
				tt.getUserProfile({
					success(res) {
						console.log("getUserProfile 调用成功：", res.userInfo);
						that.username = res.userInfo && res.userInfo.nickName
					},
					fail(res) {
						console.log("getUserProfile 调用失败", res);
						that.username = "未命名"
						that.getUserInfo1();
					},
				});
			},
			getUserInfo1: function(){
				let that = this
				tt.getUserInfo({
					withCredentials: true,
					success(res) {
						console.log(`getUserInfo 调用成功`, res.userInfo);
						that.username = res.userInfo && res.userInfo.nickName
					},
					fail(res) {
						console.log(`getUserInfo 调用失败`);
					},
				});
			},
			nextPay:function(){
				let that = this
				let phone = uni.getStorageSync('userPhone');
				if(phone && this.username ){
					console.log("phone", phone , "userName", this.username)
					
				}else {
					let userInfo = uni.getStorageSync('privateUser');
					phone = userInfo.mobile;
					this.username = userInfo.name
				}
				registerSonOrg({ phone: phone, userName:this.username })
					.then((res)=>{
						console.log('registerSonOrg', res)
						let result = res.data && res.data.data;
						// true 成功, false 失败 (加了一个限制，
						// 手机号如已存在 一个作为董事长的直播机构，则返回 false，给原型中的“这个手机号已被注销了，换个手机号吧”提示)
						if(result){
							uni.navigateTo({
								url: "/pages/officeList/officeList"
							});
						}else{
							// true 成功, false 失败 
							// (加了一个限制，手机号如已存在 一个作为董事长的直播机构，则返回 false，给原型中的“这个手机号已被注销了，换个手机号吧”提示)
							
							uni.showModal({
								title: '',
								content: '这个手机号已被注销了，换个手机号吧' ,
								showCancel: false ,
								success: function (res) {
									if (res.confirm) {
										console.log('用户点击确定');
										uni.navigateTo({
											url: "/pages/register/register-2/register-2"
										});
									} else if (res.cancel) {
										console.log('用户点击取消');
									}
								}
							});
						}
						
					})
				 
				
			},
			radioChange: function(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			}
		}
	}
</script>

<style> 
.martop20{
	margin-top: 15px;
}
.tab1{
	padding-left: 15px;
	font-size: 13px;
}
.zhuceing{
	display: block;
	margin:100px auto;
	text-align: center;
}
</style>
