<template>
	<view class="uni-container">
		<view class="uni-panel-all live-flex-panel center">

			<view class="p1" v-if="shareMesage.type == 2"> 主播{{ shareMesage.shareName}}邀请你加入ta的工作室！ </view>
			<view>
				<checkbox-group @change="checkboxChange">
					<label>
						<checkbox value="1" color="#0ca751" /><text>加入ta的工作室</text>
					</label>
				</checkbox-group>
			</view>
			<view class="live-flex-panel live-flex-item mar40">
				<navigator class="cancel" open-type="exit" target="miniProgram">退出</navigator>
				<button class=" primary" @click="goPage()">下一步</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { auth } from '@/sys/auth'
	import { dd } from '@/utils/DateFormat.js'
	// import { joinTeam } from '../../api/api.js'
	import { joinTeam } from '@/api/api.js'
	export default {
		data() {
			return {
				register5URL:"/pages/register/register-5/register-5",
				shareMesage:uni.getStorageSync('share_message'),
				agree: false,
				user:{},
				org:{},
			}
		},
		onLoad(){
			const loginUser = uni.getStorageSync('loginUser');
			this.user = loginUser
			console.log("this.user =", this.user ) 
			
			if(this.shareMesage.type){
				this.shareMesage.shareTime =  new Date(Number(this.shareMesage.shareTime)).format("yyyy-MM-dd hh:mm") 
			}
		},
		methods: {
			checkboxChange: function(){
				this.agree = !this.agree;
				console.log('this.agree =', this.agree )
			},
			goPage() {
				if(this.agree){
					console.log('this.agree ', this.agree)
					let acc = auth.getAcc()
					let mobile = acc.mobile
					if(this.user && mobile){ // 已经注册过的
						let share_message = uni.getStorageSync('share_message')
						// share_message = JSON.parse(share_message)
						console.log('share_message=', share_message)
						//  submitState 0-不展示手机号，1-展示手机号 ，oid 加入的机构id
						joinTeam({ 'submitState': 1, 'oid':share_message.oid }).then(res => {
							console.log('joinTeam = ', res)
							uni.hideLoading()
							let dd = res.data
							if(dd){
								uni.navigateTo({
									url: '/pages/officeList/officeList'
								});
							}else{
								uni.redirectTo({
									url: '/pages/redirectpage/redirectpage?path=/pages/my/privateSpace/mine/mine',
								}); 
							}
						})

					}else{ // 没注册过的
						uni.navigateTo({
							url: this.register5URL
						});
					}

					
				}
				else{
					uni.showToast({
						title: '请勾选加入ta的工作室！' ,
						icon: 'none',
						duration: 3000
					})
				}
			}, 
		}
	}
</script>

<style lang="scss">
.center{
	text-align: center;
	padding:60rpx 20rpx;
	.p1{ margin:50rpx 0; }
}
.mar40{
	margin-top: 80rpx;
	button{
		width: 180rpx;
	}
}
.cancel{
	display: inline-block;
    border: 1px solid #ddd;
    padding: 6px;
    border-radius: 4px;
    font-size: 18px;
    color: #666;
    width: 80px;
    position: relative;
    left: 70px;
}
</style>
