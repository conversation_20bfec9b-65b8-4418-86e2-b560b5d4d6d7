<template>
	<view class="uni-container">
		<pageHead title="注册主播助手" :showBack="false" />
		<view class="uni-body">
			<view class="centerTxt">
				<view>您需要注册一个“主播助手”账号！</view>
			</view>
			<view class="live-form">
				<view class="live-flex live-form-item uni-form-item ">
					<view class="title">手机号</view>
					<input focus class="uni-input mobile" placeholder-style="font-size:28rpx;" v-model="phone"  placeholder="请输入中国大陆地区的手机号" />
				</view>
				<view class="live-flex live-form-item">
					<view class="title">验证码</view>
					<input class="uni-input" type="number"  v-model="code" placeholder-style="font-size:28rpx;" placeholder="请输入验证码" />
					<button class="mini-btn primary getCode" @click="getCode">获取验证码</button>
				</view>
				<button class="primary nextStep" :loading="isloading" @click="nextStep">填写完毕，注册</button>
				<view class="uni-panel-nobg">
					<checkbox-group @change="checkboxChange">
						<label class="uni-list-cell uni-list-cell-pd">
							<view>
								<checkbox value="1" color="#0ca751" />
								同意《用户协议》和《隐私政策》
							</view>
						</label>
					</checkbox-group>
				</view>
			</view>
			<view class="mar60 uni-panel-nobg">
				<view class="bluetip marBtm20">
					系统默认团队内人员看不到您的手机号，您
					可勾选“让团队内人员看到我的手机号”
				</view>
				<checkbox-group @change="checkboxChangePhone">
						<label class="uni-list-cell uni-list-cell-pd">
							<view>
								<checkbox value="1" color="#0ca751" />
								让团队内人员看到我的手机号
							</view>
						</label>
					</checkbox-group>
			</view>
		
		</view>
		 
	</view>
</template>
<script>
	import { sendMobileCode , checkCode } from '../../../api/api.js'
	import { dd } from '@/utils/DateFormat.js'

	export default {
		data() {
			return {
				isloading:false,
				agree:false,
				showPhone: false,
				phone:"",
				code:"",
				guestToken:"",
				shouldTip: "",
				sendcodeMobile:"",
				shareMesage:uni.getStorageSync('share_message'),
				phoneSate:0

			}
		}, 
		onLoad(){
			 

		},
		methods: {
			tipSend:function(msg){
				uni.showToast({
						title: msg ,
						icon: 'none',
						duration: 3000
					})
			},
			getCode:function(){
				if(this.phone.length !== 11){
					uni.showToast({
						title:"请输入正确的手机号！",
							icon: 'none',
							duration: 3000
					})
					return false
				}
				sendMobileCode({
					phone: this.phone
				}).then((res)=>{
					console.log(' sendMobileCode res', res);
					let status = res.data.data ; // 1-成功 0-失败，3-手机号格式不对，5 验证码已经发过还未过时
					this.sendcodeMobile = this.phone
					let tip = '验证码已发送，注意查收！'
					if(status == 0 ){
						tip = '验证码发送失败'
					}else if(status == 3){
						tip = '手机号格式不对';
					}else if(status == 5){
						tip = '验证码已经发过还未过时';
					}else if(status == 4){
						this.phoneSate = 4
					}
					uni.showToast({
						title: tip ,
						icon:"none",
						duration: 3000
					})
					console.log('sendMobileCode success')
				}).catch((res)=>{
					console.log('res=',res)
					console.log(res)
					uni.showToast({
						title:'验证码发送失败，请重新发送！',
						icon:"none",
						duration: 3000
					})
				});
			
			},
			checkboxChange: function(){
				this.agree = !this.agree;
			},
			checkboxChangePhone: function(){
				this.showPhone = !this.showPhone;
			},
			joinTeamFun(){
				let submitState = this.showPhone ? 1: 0 ;
				let share_message = uni.getStorageSync('share_message')
				share_message = JSON.parse(share_message)
				console.log('share_message=', share_message)
				joinTeam({ 'submitState': submitState, 'oid': share_message.oid }).then(res => {
					console.log('joinTeam = ', res)
					let dd = res.data
					if(dd){
						uni.showToast({
							title:" 注册完毕，正在进入系统!",
							icon: 'none',
							duration: 3000
						})
					
						uni.navigateTo({
							url: '/pages/officeList/officeList'
						});
					}else{
						uni.showToast({ 
							title: '操作失败，请重试！' ,
							icon:"none",
							duration: 3000

						})
					}
					
				})
			},
			nextStep:function(){
				if(this.phoneSate == 4){
					uni.showToast({
						title: '这个号不能再注册直播助手了，请换一个！' ,
						icon:"none",
						duration: 3000

					})
					return false
				}
				let that = this
				if(this.phone.length !== 11){
					uni.showToast({
						title:"手机号码不正确！",
							icon: 'none',
							duration: 3000
					})
					return false
				}
				if(this.code.length == 0){
					uni.showToast({
						title:"请输入正确的验证码！",
							icon: 'none',
							duration: 3000
					})
					return false
				}
				console.log("this.agree", this.agree)
				if(!this.agree){
					this.tipSend("请勾选同意《用户协议》和《隐私政策》!")
					// uni.showToast({
					// 	title:"请勾选同意《用户协议》和《隐私政策》!",
					// 	icon: 'none',
					// 	duration: 3000
					// })
					return false
				}
				
				checkCode({ phone: this.phone, code:this.code })
				.then((res)=>{
					console.log("checkCode, res:", res.data)
					let data = res.data && res.data.data
					console.log('data1 = ', data)
					if(data == "登录成功"){
						console.log('data=', data)
						uni.showToast({
							title:" 注册完毕!",
							icon: 'none',
							duration: 3000,
							success:function(){
								uni.redirectTo({
									url: `/pages/startPage/startPage?shareTime=${ that.shareMesage.shareTime }&shareName=${ that.shareMesage.shareName }&type=${ that.shareMesage.type }&oid=${ that.shareMesage.oid }`,
								}); 
							}
						})
						
					}else{
						console.log("shibai")
						let err = res.data.error
						uni.showModal({
							title: '',
							content: err.message ,
							showCancel: false ,
							success: function (res) {
								if (res.confirm) {
									console.log('用户点击确定');
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						});
					 
					}
					
				}).catch(()=>{
					uni.showToast({
						title:'登录失败！',
						icon:'none'
					})
				});
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mar60{
		margin-top: 100rpx;
	}
	.marBtm20{
		margin-bottom: 30rpx;
	}
	.checkbox{
		border-radius: 0;
	}
	.centerTxt{
		text-align: center;
		margin: 50rpx auto;
		font-size:32rpx ;
		
	}
	.bluetip{
		color: #58a9b6;
		font-size: 0.8em;
		margin-top: 5rpx;
	}
	.live-flex{
		display: flex;
	}
	.nextStep{
		margin-top: 60rpx;
	}
	.getCode{ width: 100px; position: relative; top:-12rpx;   }
	.live-form{
		margin-top:32rpx;
		.live-form-item{
			line-height: 60rpx;
			border-bottom: 1px solid #ddd;
			margin-top: 50rpx;
			.title{ width: 120rpx; position: relative; bottom: 6rpx; }
			.mobile{ flex: 2; }
			.mini-btn{
				font-size: 24rpx;
			}
		}
	}
	
</style>
