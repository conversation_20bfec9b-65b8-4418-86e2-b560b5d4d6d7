<template>
	<view>
		<!-- <pageHead title="主播助手价格政策" :showBack="true" :runMyBackFun="false" @myBackFun="myBackFun2()"> -->
		<pageHead title="主播助手价格政策" :showBack="false">
			<!-- <view slot="leftBts">hhh</view> -->
			<!-- <template v-slot:rightBtns>
				<view>share</view>
			</template> -->
		</pageHead>
		<view class="uni-panel-all martop30">
			<view><text class="star"></text>本系统的使用费为每日9元，需预缴费</view>
			<view><text class="star"></text>账号可注销，注销时将按日扣费，余额原路退还</view>
			<view><text class="star"></text>主播助手分为体验版与标准版</view>
			<view><text class="star"></text>当前您只能注册体验版</view>
			<view><text class="star"></text>体验版已能满足您的基本需要</view>
			<view>
				<text><text class="star"></text>体验版有的套餐优惠幅度较大，具体如下：</text>
				<view class="tab1">
					<view>一次缴纳5天的使用费，无优惠，缴费45元</view>
					<view>一次缴纳21天的使用费，优惠至138元</view>
					<view>一次缴纳90天的使用费，优惠至388元</view>
					<view>一次缴纳365天的使用费，优惠至888元</view>
				</view>
			</view>
			<view><text class="star"></text>标准版价格暂无优惠</view>
			<view><text class="star"></text>注销时的按日扣费，按每日9元计，与套餐无关</view>
		</view>
	</view>
</template>

<script>
	// import pageHead form "@/component/page-head/page-head.vue"
	export default {
		data() {
			return {
			}
		},
		methods: {
			myBackFun2:function(){
				console.log('myBackFun')
			}
		}
	}
</script>

<style>
.martop30{
	margin-top: 30px;
}
.tab1{
	padding-left: 30px;
}
.star{
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url('@/static/img/st.png') no-repeat;
	background-size: 16px;
	margin-right: 10px;
	position: relative;
	top:2px;
}
</style>
