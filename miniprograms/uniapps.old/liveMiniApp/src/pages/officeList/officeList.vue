<template>
	<view class="uni-container">
		<pageHead title="主播工作室列表" :showBack="false"/>
		<view class="officeList">
			<view v-for="(item , index) in list" :key="index"  @click="goOffice(item)">{{ item.name }}</view>
		</view>
	</view>
</template>

<script>
	import { organizationList, getTerritoryAuthInfo, sureLogin  } from '../../api/api.js'
	import {webViewRoot} from "../../../config.js"
	export default {
		data() {
			return {
				list :[],
			}
		},
		onLoad() {
			let that = this;
			this.getOfficeList()
			
		},
		methods: {
			getOfficeList:function(){
				organizationList().then((res) => {
					console.log('organizationList', res)
					this.list = (res.data.organizationList) || [] 
					console.log('机构列表', this.list)
					uni.setStorageSync('officeList', this.list); 
				})
			},
			goOffice:function(officeItem){
				console.log('officeItem', officeItem)
				if(officeItem.code === "liveHelper"){
					uni.setStorageSync('officeInfo', officeItem);
					let userPhone = uni.getStorageSync('userPhone');
					// 获取用户信息
					sureLogin({ "mobile": userPhone, "oid": officeItem.id }).then((res)=>{
						console.log('sureLogin', res)
						let userInfo = uni.getStorageSync('userInfo')|| {};
						console.log('userInfo', userInfo);
						let userLoginDto = res.data.userLoginDto
						let userAuth = res.data.user

						userInfo.orgU = userLoginDto
						userInfo.orgUserAuth = userAuth
						uni.setStorageSync('userInfo', userInfo);
						console.log('userInfo', userInfo)

						let user1 = uni.getStorageSync('user') 
						user1 = JSON.parse(user1)
						if(user1.roleCode == 'staff'){
							uni.redirectTo({ 
								url: '/pages/redirectpage/redirectpage?path=/pages/home/<USER>/manage',
							}); 
						}else{
							uni.redirectTo({ 
								url: '/pages/redirectpage/redirectpage?path=/pages/home/<USER>/data',
							}); 
						}
					})
				}else{
					uni.showToast({
						title: '抱歉，抖音短暂不支持该类机构的登陆！',
						duration: 1000,
						icon:'none'
					});
				}
				
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	.officeList{
		padding-bottom: 200rpx;
		&>view{
			padding:20rpx 32rpx;
		}
		&>view:nth-child(odd){
			background-color: #fff;
		}
	}
</style>
