<template>
	<view class="live-table">
		<view class="live-head">
			<view :style="{flex:(item.flex || 1) }" class="live-td" v-for="(item, index) in columns" :key="item.key" v-html="item.title">
			 
			</view>
		</view>
		<view class="live-body">
			<view class="live-tr" v-for="(item2, index2) in list" :key="index2"	@longpress="onLongPress(item2, index2)">
				<view :style="{flex:(item3.flex || 1) }" class="live-td" v-for="(item3, index3) in columns" :key="index3">
				 {{ item2[item3.key] }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"liveTable",
		props: {
			columns: {
				type: Array,
				default: [{
					title: "ID",
					key: "id"
				}]
			},
			list: {
				type: Array,
				default: []
			}, 
		},
		data() {
			return {
				time:"",
				timeOutEvent: null,
			};
		},
		methods:{ 
			onLongPress(item, index) {  
				this.$emit('longPress',item, index)
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	.live-table{
		.live-head{
			background-color: #dade76;
		}
		.live-head, .live-tr{
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.live-td{
				flex: 1;
				// overflow: hidden;
				// white-space: nowrap; 
				// text-overflow: ellipsis; 
				text-align: center;
				line-height: 50rpx;
				padding:20rpx 8rpx;
				font-size: 26rpx;
			}
		}
		.live-body{
			background-color: #fff;
			.live-tr:nth-child(odd){
				background-color: #f8f8f8;
			}
		}
	}
	
</style>
