<template name="pageHead">
	<view class="page-head">
		<view>
			<view v-if="showBack" class="back" @click="backBtn()">返回</view>
			<slot v-if="!showBack" name= "leftBts"/> 
		</view>
		<view class="page-title">{{title}}</view>
		<view>
			<slot name="rightBtns"></slot>
		</view>
	</view>
</template>
<script>
	export default {
		name: "pageHead",
		props: {
			title: {
				type: String,
				default: "提示"
			},
			showBack: {
				type: Boolean,
				default: false
			},
			runMyBackFun: {
				type: Boolean,
				default: false
			},
		},
		methods:{
			backBtn:function(){
				console.log('runMyBackFun', this.runMyBackFun)
				if (!this.runMyBackFun) {
					uni.navigateBack()
				} else {
					console.log('reback')
					this.$emit('myBackFun')
				}
			}
		}
	}
</script>
<style lang="scss" scoped>
	// @import "../../uni.scss";  
	@import "../../uni.scss";  
	.page-head{
		background-color: $live-page-head-bg;
		display: flex;
		height: 80rpx;
		justify-content: space-between;
		color: #fff;
		line-height: 80rpx;
		text-align: center;
		overflow: hidden;
		.back{
			width:80rpx;
		}
		.page-title{
			font-size: 30rpx;
			color: #fff;
		}
	}
</style>