<template name="tabBar">
	<view class="">
		<view class="uni-tabbar" v-if="mainTab === 1">
			<view v-if="isShowData" @click="tabTo('/pages/home/<USER>/data')">
				<view class="icon" :class="activeIndex === 0 ? 'icon-data-active' :'icon-data'"></view>
				<view class="txt" :class="activeIndex === 0 ? 'txt-active' :''">数据</view>
			</view>
			<view @click="tabTo('/pages/home/<USER>/manage')">
				<view class="icon" :class="activeIndex === 1 ? 'icon-manage-active' :'icon-manage'"></view>
				<view class="txt" :class="activeIndex === 1 ? 'txt-active' :''">管理</view>
			</view>
			<view @click="tabTo('/pages/home/<USER>/handle')">
				<view class="icon" :class="activeIndex === 6 ? 'icon-handle-active' :'icon-handle'"></view>
				<view class="txt" :class="activeIndex === 6 ? 'txt-active' :''">处理</view>
			</view>
			<view @click="tabTo('/pages/home/<USER>/my')" >
				<view class="icon" :class="activeIndex === 2 ? 'icon-my-active' :'icon-my'"></view>
				<view class="txt" :class="activeIndex === 2 ? 'txt-active' :''">我的</view>
			</view>
		</view> 
		<view class="uni-tabbar" v-else="mainTab === 2">
			<view @click="tabTo('/pages/my/privateSpace/life/life')">
				<view class="icon icon-life"></view>
				<view class="txt" :class="activeIndex === 1 ? 'txt-active2' :''">生活</view>
			</view>
			<view @click="tabTo('/pages/my/privateSpace/mailList/mailList')">
				<view class="icon icon-mailList"></view>
				<view class="txt" :class="activeIndex === 2 ? 'txt-active2' :''">通讯录</view>
			</view>
			<view @click="tabTo('/pages/my/privateSpace/manage/manage')">
				<view class="icon icon-manage-active"></view>
				<view class="txt" :class="activeIndex === 3 ? 'txt-active2' :''">管理</view>
			</view>
			<view @click="tabTo('/pages/my/privateSpace/remind/remind')">
				<view class="icon icon-remind"></view>
				<view class="txt" :class="activeIndex === 4 ? 'txt-active2' :''">提醒</view>
			</view>
			<view @click="tabTo('/pages/my/privateSpace/mine/mine')">
				<view class="icon icon-my-active"></view>
				<view class="txt" :class="activeIndex === 5 ? 'txt-active2' :''">我的</view>
			</view>

		</view>
		
	</view>
</template>
<script>
	export default {
		name: "tabBar",
		props: {
			activeIndex: {
				type: Number,
				default: 0
			},
			mainTab: {
				type: Number,
				default: 1
			},
			isShowData:{
				type: Boolean,
				default: false
			}
		},
		onLoad() {
			this.login(this)
		},
		methods:{
			tabTo:function(path){
				// this.$emit('tabTo', path)
				uni.redirectTo({
					url: path 
				});
			}
		}
	}
</script>
<style lang="scss" scoped>
	// @import "../../uni.scss";  
	@import "../../uni.scss";  
	// "iconPath": "static/img/tabbar/home.png",
	// 			"selectedIconPath": "static/img/tabbar/homeactive.png",
	.uni-tabbar{
		display: flex;
		// height:56px;
		background: #fff;
		// border-top: 1px solid #333;
		position: fixed;
		bottom: 0;
		width: 100%;
		z-index: 1;
		padding:4px 0;
		>view{ flex: 1;  text-align: center;  }
		.icon-data{ background: url("../../static/img/tabbar/data2.png"); }
		.icon-data-active{ background: url("../../static/img/tabbar/data1.png"); }
		.icon-manage{ background: url("../../static/img/tabbar/manage2.png"); }
		.icon-manage-active{ background: url("../../static/img/tabbar/manage1.png"); }
		.icon-handle{ background: url("../../static/img/tabbar/handle.png"); }
		.icon-handle-active{ background: url("../../static/img/tabbar/handleA.png"); }
		.icon-my{ background: url("../../static/img/tabbar/my2.png"); }
		.icon-my-active{ background: url("../../static/img/tabbar/my1.png"); }

		.icon-life{ background: url("../../static/img/tabbar/life.png"); }
		.icon-mailList{ background: url("../../static/img/tabbar/mailList.png"); }
		.icon-remind{ background: url("../../static/img/tabbar/remind.png"); }
 
		.icon{
			width: 20px;
			height: 20px;
			background-color: #fff;
			margin:3px auto;
			background-size: 100%;
		}
		.txt{
			font-size: 10px;
		}
		.txt-active{
			color:$live-page-head-bg
		}
		.txt-active2{
			color:$live-page-head-bg
		}

	}
</style>